"""
VietLaw.AI SEO Service
Search Engine Optimization and Answer Engine Optimization
"""

import logging
import re
from typing import Dict, List, Optional, Any
from datetime import datetime
import xml.etree.ElementTree as ET
from urllib.parse import urljoin
import asyncio

from app.core.config import settings
from app.models.document import LegalDocument
from app.models.user import UserQuery
from app.core.database import get_db_session_context
from sqlalchemy import select, func, desc

logger = logging.getLogger(__name__)


class SEOService:
    """SEO and AEO optimization service"""
    
    def __init__(self):
        self.base_url = settings.BASE_URL
        self.sitemap_limit = 50000  # Google's limit
        
    def generate_meta_tags(
        self,
        title: str,
        description: str,
        keywords: List[str] = None,
        canonical_url: str = None,
        og_image: str = None,
        document_data: Dict = None
    ) -> Dict[str, str]:
        """Generate comprehensive meta tags for SEO"""
        try:
            # Clean and optimize title
            clean_title = self._clean_text(title)
            if len(clean_title) > 60:
                clean_title = clean_title[:57] + "..."
            
            # Clean and optimize description
            clean_description = self._clean_text(description)
            if len(clean_description) > 160:
                clean_description = clean_description[:157] + "..."
            
            # Generate keywords
            if not keywords:
                keywords = self._extract_keywords(f"{title} {description}")
            
            meta_tags = {
                "title": f"{clean_title} | VietLaw.AI",
                "description": clean_description,
                "keywords": ", ".join(keywords[:10]),  # Limit to 10 keywords
                "canonical": canonical_url or "",
                "og:title": clean_title,
                "og:description": clean_description,
                "og:image": og_image or f"{self.base_url}/assets/og-default.jpg",
                "og:type": "article" if document_data else "website",
                "og:site_name": "VietLaw.AI",
                "twitter:card": "summary_large_image",
                "twitter:title": clean_title,
                "twitter:description": clean_description,
                "robots": "index, follow, max-snippet:-1, max-image-preview:large"
            }
            
            # Add document-specific meta tags
            if document_data:
                meta_tags.update({
                    "article:published_time": document_data.get("issue_date", ""),
                    "article:modified_time": document_data.get("effective_date", ""),
                    "article:author": document_data.get("issuing_authority", ""),
                    "article:section": document_data.get("document_type", ""),
                    "document:type": document_data.get("document_type", ""),
                    "document:number": document_data.get("document_number", ""),
                    "document:authority": document_data.get("issuing_authority", ""),
                })
            
            return meta_tags
            
        except Exception as e:
            logger.error(f"Error generating meta tags: {e}")
            return {}
    
    def generate_structured_data(
        self,
        page_type: str,
        data: Dict,
        breadcrumbs: List[Dict] = None
    ) -> Dict:
        """Generate structured data for different page types"""
        try:
            structured_data = {
                "@context": "https://schema.org",
                "@graph": []
            }
            
            # Organization data
            organization = {
                "@type": "Organization",
                "@id": f"{self.base_url}/#organization",
                "name": "VietLaw.AI",
                "url": self.base_url,
                "logo": {
                    "@type": "ImageObject",
                    "url": f"{self.base_url}/assets/logo.png",
                    "width": 200,
                    "height": 60
                },
                "description": "Hệ thống AI tiên tiến cho pháp luật Việt Nam",
                "contactPoint": {
                    "@type": "ContactPoint",
                    "telephone": "+84-xxx-xxx-xxx",
                    "contactType": "customer service",
                    "availableLanguage": ["Vietnamese", "English"]
                },
                "areaServed": {
                    "@type": "Country",
                    "name": "Vietnam"
                }
            }
            structured_data["@graph"].append(organization)
            
            # Website data
            website = {
                "@type": "WebSite",
                "@id": f"{self.base_url}/#website",
                "url": self.base_url,
                "name": "VietLaw.AI",
                "description": "Trợ lý AI Pháp luật Việt Nam",
                "publisher": {"@id": f"{self.base_url}/#organization"},
                "inLanguage": "vi-VN",
                "potentialAction": {
                    "@type": "SearchAction",
                    "target": {
                        "@type": "EntryPoint",
                        "urlTemplate": f"{self.base_url}/search?q={{search_term_string}}"
                    },
                    "query-input": "required name=search_term_string"
                }
            }
            structured_data["@graph"].append(website)
            
            # Page-specific structured data
            if page_type == "legal_document":
                document_data = self._generate_legal_document_schema(data)
                structured_data["@graph"].append(document_data)
            elif page_type == "search_results":
                search_data = self._generate_search_results_schema(data)
                structured_data["@graph"].append(search_data)
            elif page_type == "article":
                article_data = self._generate_article_schema(data)
                structured_data["@graph"].append(article_data)
            
            # Breadcrumbs
            if breadcrumbs:
                breadcrumb_data = self._generate_breadcrumb_schema(breadcrumbs)
                structured_data["@graph"].append(breadcrumb_data)
            
            return structured_data
            
        except Exception as e:
            logger.error(f"Error generating structured data: {e}")
            return {}
    
    def _generate_legal_document_schema(self, data: Dict) -> Dict:
        """Generate schema for legal documents"""
        return {
            "@type": "LegalDocument",
            "@id": f"{self.base_url}/document/{data.get('id')}",
            "name": data.get("title", ""),
            "description": data.get("summary", ""),
            "url": f"{self.base_url}/document/{data.get('id')}",
            "datePublished": data.get("issue_date", ""),
            "dateModified": data.get("effective_date", data.get("issue_date", "")),
            "publisher": {
                "@type": "Organization",
                "name": data.get("issuing_authority", ""),
                "url": self.base_url
            },
            "inLanguage": "vi-VN",
            "isPartOf": {"@id": f"{self.base_url}/#website"},
            "about": {
                "@type": "Thing",
                "name": "Vietnamese Law",
                "description": "Pháp luật Việt Nam"
            },
            "additionalProperty": [
                {
                    "@type": "PropertyValue",
                    "name": "documentType",
                    "value": data.get("document_type", "")
                },
                {
                    "@type": "PropertyValue",
                    "name": "documentNumber",
                    "value": data.get("document_number", "")
                },
                {
                    "@type": "PropertyValue",
                    "name": "status",
                    "value": data.get("status", "")
                }
            ]
        }
    
    def _generate_search_results_schema(self, data: Dict) -> Dict:
        """Generate schema for search results pages"""
        return {
            "@type": "SearchResultsPage",
            "@id": f"{self.base_url}/search",
            "name": f"Kết quả tìm kiếm: {data.get('query', '')}",
            "description": f"Tìm thấy {data.get('total_results', 0)} kết quả cho '{data.get('query', '')}'",
            "url": f"{self.base_url}/search?q={data.get('query', '')}",
            "mainEntity": {
                "@type": "ItemList",
                "numberOfItems": data.get("total_results", 0),
                "itemListElement": [
                    {
                        "@type": "ListItem",
                        "position": i + 1,
                        "item": {
                            "@type": "LegalDocument",
                            "name": result.get("title", ""),
                            "url": f"{self.base_url}/document/{result.get('id', '')}"
                        }
                    }
                    for i, result in enumerate(data.get("results", [])[:10])
                ]
            }
        }
    
    def _generate_article_schema(self, data: Dict) -> Dict:
        """Generate schema for articles/blog posts"""
        return {
            "@type": "Article",
            "@id": f"{self.base_url}/article/{data.get('id')}",
            "headline": data.get("title", ""),
            "description": data.get("description", ""),
            "url": f"{self.base_url}/article/{data.get('id')}",
            "datePublished": data.get("published_date", ""),
            "dateModified": data.get("modified_date", data.get("published_date", "")),
            "author": {
                "@type": "Organization",
                "name": "VietLaw.AI",
                "url": self.base_url
            },
            "publisher": {"@id": f"{self.base_url}/#organization"},
            "mainEntityOfPage": {"@id": f"{self.base_url}/article/{data.get('id')}"},
            "image": {
                "@type": "ImageObject",
                "url": data.get("image", f"{self.base_url}/assets/default-article.jpg"),
                "width": 1200,
                "height": 630
            },
            "articleSection": data.get("category", "Legal Technology"),
            "inLanguage": "vi-VN"
        }
    
    def _generate_breadcrumb_schema(self, breadcrumbs: List[Dict]) -> Dict:
        """Generate breadcrumb schema"""
        return {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": i + 1,
                    "name": crumb.get("name", ""),
                    "item": crumb.get("url", "")
                }
                for i, crumb in enumerate(breadcrumbs)
            ]
        }
    
    async def generate_sitemap(self) -> str:
        """Generate XML sitemap"""
        try:
            # Create root element
            urlset = ET.Element("urlset")
            urlset.set("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9")
            urlset.set("xmlns:xhtml", "http://www.w3.org/1999/xhtml")
            
            # Add homepage
            self._add_url_to_sitemap(
                urlset,
                self.base_url,
                priority="1.0",
                changefreq="daily"
            )
            
            # Add static pages
            static_pages = [
                ("/search", "0.9", "daily"),
                ("/about", "0.5", "monthly"),
                ("/contact", "0.5", "monthly"),
                ("/privacy", "0.3", "yearly"),
                ("/terms", "0.3", "yearly"),
                ("/help", "0.6", "weekly"),
            ]
            
            for path, priority, changefreq in static_pages:
                self._add_url_to_sitemap(
                    urlset,
                    urljoin(self.base_url, path),
                    priority=priority,
                    changefreq=changefreq
                )
            
            # Add legal documents
            async with get_db_session_context() as db:
                # Get active documents
                result = await db.execute(
                    select(LegalDocument.id, LegalDocument.updated_at)
                    .where(LegalDocument.status == "active")
                    .order_by(desc(LegalDocument.updated_at))
                    .limit(self.sitemap_limit - 100)  # Reserve space for static pages
                )
                
                documents = result.fetchall()
                
                for doc_id, updated_at in documents:
                    self._add_url_to_sitemap(
                        urlset,
                        urljoin(self.base_url, f"/document/{doc_id}"),
                        lastmod=updated_at.isoformat() if updated_at else None,
                        priority="0.8",
                        changefreq="monthly"
                    )
            
            # Convert to string
            tree = ET.ElementTree(urlset)
            ET.indent(tree, space="  ", level=0)
            
            import io
            output = io.StringIO()
            tree.write(output, encoding="unicode", xml_declaration=True)
            
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error generating sitemap: {e}")
            return ""
    
    def _add_url_to_sitemap(
        self,
        urlset: ET.Element,
        url: str,
        lastmod: str = None,
        changefreq: str = None,
        priority: str = None
    ):
        """Add URL to sitemap"""
        url_elem = ET.SubElement(urlset, "url")
        
        loc_elem = ET.SubElement(url_elem, "loc")
        loc_elem.text = url
        
        if lastmod:
            lastmod_elem = ET.SubElement(url_elem, "lastmod")
            lastmod_elem.text = lastmod
        
        if changefreq:
            changefreq_elem = ET.SubElement(url_elem, "changefreq")
            changefreq_elem.text = changefreq
        
        if priority:
            priority_elem = ET.SubElement(url_elem, "priority")
            priority_elem.text = priority
    
    async def generate_robots_txt(self) -> str:
        """Generate robots.txt file"""
        try:
            robots_content = f"""User-agent: *
Allow: /

# Sitemaps
Sitemap: {self.base_url}/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1

# Disallow admin and API endpoints
Disallow: /admin/
Disallow: /api/
Disallow: /auth/
Disallow: /_next/
Disallow: /static/

# Allow important pages
Allow: /search
Allow: /document/
Allow: /help
Allow: /about

# Block specific user agents if needed
User-agent: AhrefsBot
Crawl-delay: 10

User-agent: MJ12bot
Crawl-delay: 10

# Host directive
Host: {self.base_url.replace('https://', '').replace('http://', '')}
"""
            return robots_content
            
        except Exception as e:
            logger.error(f"Error generating robots.txt: {e}")
            return ""
    
    def _extract_keywords(self, text: str, max_keywords: int = 15) -> List[str]:
        """Extract keywords from text for SEO"""
        try:
            # Vietnamese legal keywords
            legal_keywords = [
                "pháp luật", "luật", "nghị định", "thông tư", "quyết định",
                "hiến pháp", "bộ luật", "quy định", "điều", "khoản",
                "quyền", "nghĩa vụ", "trách nhiệm", "vi phạm", "xử phạt",
                "tòa án", "kiểm sát", "công an", "quốc hội", "chính phủ"
            ]
            
            # Clean text
            clean_text = self._clean_text(text.lower())
            words = clean_text.split()
            
            # Find legal keywords in text
            found_keywords = []
            for keyword in legal_keywords:
                if keyword in clean_text:
                    found_keywords.append(keyword)
            
            # Add frequent words (simplified approach)
            word_freq = {}
            for word in words:
                if len(word) > 3 and word.isalpha():
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            # Sort by frequency and add to keywords
            frequent_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            for word, freq in frequent_words[:max_keywords - len(found_keywords)]:
                if word not in found_keywords:
                    found_keywords.append(word)
            
            return found_keywords[:max_keywords]
            
        except Exception as e:
            logger.error(f"Error extracting keywords: {e}")
            return []
    
    def _clean_text(self, text: str) -> str:
        """Clean text for SEO purposes"""
        try:
            # Remove HTML tags
            text = re.sub(r'<[^>]+>', '', text)
            
            # Remove extra whitespace
            text = re.sub(r'\s+', ' ', text)
            
            # Remove special characters but keep Vietnamese characters
            text = re.sub(r'[^\w\sàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]', '', text)
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error cleaning text: {e}")
            return text
    
    async def get_popular_search_terms(self, limit: int = 20) -> List[Dict]:
        """Get popular search terms for SEO insights"""
        try:
            async with get_db_session_context() as db:
                result = await db.execute(
                    select(
                        UserQuery.query_text,
                        func.count(UserQuery.id).label("count")
                    )
                    .where(UserQuery.created_at >= datetime.utcnow() - timedelta(days=30))
                    .group_by(UserQuery.query_text)
                    .order_by(desc("count"))
                    .limit(limit)
                )
                
                popular_terms = []
                for query_text, count in result.fetchall():
                    popular_terms.append({
                        "query": query_text,
                        "count": count,
                        "keywords": self._extract_keywords(query_text, 5)
                    })
                
                return popular_terms
                
        except Exception as e:
            logger.error(f"Error getting popular search terms: {e}")
            return []


# Global instance
seo_service = SEOService()
