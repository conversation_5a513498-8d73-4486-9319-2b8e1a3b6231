#!/bin/bash

# VietLaw.AI Dependency Installation Script
# Handles installation of all project dependencies with error handling

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Python version
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed"
        exit 1
    fi
    
    local python_version=$(python3 --version | cut -d' ' -f2)
    local required_version="3.9"
    
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)"; then
        log_error "Python 3.9+ is required. Current version: $python_version"
        exit 1
    fi
    
    # Check Node.js version
    if ! command -v node &> /dev/null; then
        log_error "Node.js is required but not installed"
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2)
    local required_node_version="18"
    
    if ! node -e "process.exit(parseInt(process.version.slice(1)) >= 18 ? 0 : 1)"; then
        log_error "Node.js 18+ is required. Current version: $node_version"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is required but not installed"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

install_backend_dependencies() {
    log_info "Installing backend dependencies..."
    
    cd "$PROJECT_ROOT/backend"
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        log_info "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate || source venv/Scripts/activate
    
    # Install essential build tools first
    log_info "Installing essential build tools..."
    python -m pip install --upgrade pip>=23.0
    pip install --upgrade setuptools>=65.0 wheel>=0.38.0

    # Install dependencies with error handling
    log_info "Installing Python packages..."

    # Install torch first with specific index for compatibility
    log_info "Installing PyTorch..."
    pip install torch>=2.2.0,<2.8.0 --index-url https://download.pytorch.org/whl/cpu

    # Install other dependencies
    pip install -r requirements.txt
    
    log_success "Backend dependencies installed successfully"
}

install_frontend_dependencies() {
    log_info "Installing frontend dependencies..."
    
    cd "$PROJECT_ROOT/frontend"
    
    # Clear npm cache if needed
    log_info "Clearing npm cache..."
    npm cache clean --force
    
    # Install dependencies
    log_info "Installing Node.js packages..."
    npm install
    
    log_success "Frontend dependencies installed successfully"
}

install_integration_dependencies() {
    log_info "Installing integration dependencies..."
    
    # Microsoft Office integration
    cd "$PROJECT_ROOT/integrations/office"
    if [ -f "package.json" ]; then
        npm install
    fi
    
    # Google Workspace integration
    cd "$PROJECT_ROOT/integrations/google-workspace"
    if [ -f "package.json" ]; then
        npm install
    fi
    
    log_success "Integration dependencies installed successfully"
}

verify_installation() {
    log_info "Verifying installation..."
    
    # Test backend imports
    cd "$PROJECT_ROOT/backend"
    source venv/bin/activate || source venv/Scripts/activate
    
    python -c "
import fastapi
import sqlalchemy
import torch
import sentence_transformers
import underthesea
print('Backend dependencies verified successfully')
" || {
        log_error "Backend dependency verification failed"
        exit 1
    }
    
    # Test frontend build
    cd "$PROJECT_ROOT/frontend"
    npm run type-check || {
        log_error "Frontend type checking failed"
        exit 1
    }
    
    log_success "Installation verification completed"
}

fix_common_issues() {
    log_info "Applying fixes for common issues..."
    
    # Fix torch installation issues
    cd "$PROJECT_ROOT/backend"
    source venv/bin/activate || source venv/Scripts/activate
    
    # Reinstall torch with CPU-only version for compatibility
    pip uninstall torch torchvision torchaudio -y
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
    
    # Fix npm issues
    cd "$PROJECT_ROOT/frontend"
    
    # Remove node_modules and package-lock.json
    rm -rf node_modules package-lock.json
    
    # Reinstall with legacy peer deps flag
    npm install --legacy-peer-deps
    
    log_success "Common issues fixed"
}

show_usage() {
    cat << EOF
VietLaw.AI Dependency Installation Script

Usage: $0 [OPTIONS]

Options:
    --backend-only      Install only backend dependencies
    --frontend-only     Install only frontend dependencies
    --fix-issues        Fix common dependency issues
    --verify-only       Only verify existing installation
    -h, --help          Show this help message

Examples:
    $0                  # Install all dependencies
    $0 --backend-only   # Install only backend
    $0 --fix-issues     # Fix common issues

EOF
}

main() {
    log_info "Starting VietLaw.AI dependency installation..."
    
    check_prerequisites
    
    case "${1:-all}" in
        "--backend-only")
            install_backend_dependencies
            ;;
        "--frontend-only")
            install_frontend_dependencies
            ;;
        "--fix-issues")
            fix_common_issues
            ;;
        "--verify-only")
            verify_installation
            ;;
        "all"|"")
            install_backend_dependencies
            install_frontend_dependencies
            install_integration_dependencies
            verify_installation
            ;;
        "-h"|"--help")
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
    
    log_success "Dependency installation completed successfully!"
    
    # Show next steps
    echo ""
    log_info "Next steps:"
    echo "1. Copy .env.example to .env and configure your environment variables"
    echo "2. Start the database: docker-compose up -d postgres redis"
    echo "3. Run database migrations: cd backend && python migrate.py"
    echo "4. Start the backend: cd backend && source venv/bin/activate && uvicorn main:app --reload"
    echo "5. Start the frontend: cd frontend && npm run dev"
}

# Parse command line arguments
if [[ $# -gt 0 ]]; then
    main "$1"
else
    main
fi
