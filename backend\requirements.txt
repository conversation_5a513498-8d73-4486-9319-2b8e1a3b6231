# VietLaw.AI Backend Dependencies

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Vector Database
pgvector==0.2.4

# Redis
redis==5.0.1
aioredis==2.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# AI/ML Libraries
openai==1.3.7
sentence-transformers==2.2.2
transformers==4.36.2
torch>=2.2.0,<2.8.0
numpy==1.24.4
scikit-learn==1.3.2
pandas==2.1.4

# Vietnamese NLP
underthesea==6.7.0
pyvi==0.1.1
vncorenlp==1.0.3

# HTTP Clients
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Background Tasks
celery==5.3.4
flower==2.0.1

# File Processing
python-docx==1.1.0
PyPDF2==3.0.1
python-magic==0.4.27
openpyxl==3.1.2

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
factory-boy==3.3.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Date/Time
python-dateutil==2.8.2
pytz==2023.3

# Utilities
click==8.1.7
rich==13.7.0
tqdm==4.66.1

# Email
fastapi-mail==1.4.1

# Rate Limiting
slowapi==0.1.9

# CORS
fastapi-cors==0.0.6

# Validation
email-validator==2.1.0
phonenumbers==8.13.26

# Caching
aiocache==0.12.2

# Image Processing
Pillow==10.1.0

# Text Processing
beautifulsoup4==4.12.2
lxml==4.9.3
markdown==3.5.1

# API Documentation
fastapi-users==12.1.2

# Deployment
gunicorn==21.2.0

# Cloud Storage
boto3==1.34.0
google-cloud-storage==2.10.0

# Monitoring
psutil==5.9.6

# Vietnamese Language Support
regex==2023.10.3
unicodedata2==15.1.0
