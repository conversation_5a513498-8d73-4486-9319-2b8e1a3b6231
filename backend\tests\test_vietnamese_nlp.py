"""
VietLaw.AI Vietnamese NLP Service Tests
Comprehensive testing for Vietnamese legal text processing
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import numpy as np

from app.services.vietnamese_nlp import VietnameseNLPService, vietnamese_nlp
from app.services.legal_terminology import legal_terminology


class TestVietnameseNLPService:
    """Test cases for Vietnamese NLP Service"""
    
    @pytest.fixture
    def nlp_service(self):
        """Create NLP service instance for testing"""
        return VietnameseNLPService()
    
    @pytest.fixture
    def sample_legal_text(self):
        """Sample Vietnamese legal text for testing"""
        return """
        Điều 1. Phạm vi điều chỉnh
        Luật này quy định về quyền và nghĩa vụ của người lao động và người sử dụng lao động trong quan hệ lao động; 
        quản lý nhà nước về lao động; trách nhiệm của c<PERSON> quan, tổ chức, cá nhân có liên quan đến quan hệ lao động.
        
        Điều 2. <PERSON><PERSON><PERSON> tượng áp dụng
        1. Luật này áp dụng đối với người lao động, người sử dụng lao động trong quan hệ lao động tại Việt Nam.
        2. Trường hợp điều ước quốc tế mà Cộng hòa xã hội chủ nghĩa Việt Nam là thành viên có quy định khác với quy định của Luật này thì áp dụng quy định của điều ước quốc tế đó.
        """
    
    @pytest.mark.asyncio
    async def test_clean_text(self, nlp_service, sample_legal_text):
        """Test text cleaning functionality"""
        # Test basic cleaning
        dirty_text = "  Điều 1.   Phạm vi điều chỉnh  \n\n  "
        cleaned = await nlp_service.clean_text(dirty_text)
        
        assert cleaned == "Điều 1. Phạm vi điều chỉnh"
        assert not cleaned.startswith(" ")
        assert not cleaned.endswith(" ")
        
        # Test HTML removal
        html_text = "<p>Điều 1. <strong>Phạm vi</strong> điều chỉnh</p>"
        cleaned_html = await nlp_service.clean_text(html_text)
        
        assert "<p>" not in cleaned_html
        assert "<strong>" not in cleaned_html
        assert "Điều 1. Phạm vi điều chỉnh" in cleaned_html
    
    @pytest.mark.asyncio
    async def test_segment_sentences(self, nlp_service, sample_legal_text):
        """Test sentence segmentation"""
        sentences = await nlp_service.segment_sentences(sample_legal_text)
        
        assert len(sentences) > 0
        assert any("Điều 1. Phạm vi điều chỉnh" in sentence for sentence in sentences)
        assert any("người lao động" in sentence for sentence in sentences)
        
        # Test empty text
        empty_sentences = await nlp_service.segment_sentences("")
        assert empty_sentences == []
    
    @pytest.mark.asyncio
    async def test_tokenize_text(self, nlp_service):
        """Test text tokenization"""
        text = "Người lao động có quyền được bảo vệ."
        tokens = await nlp_service.tokenize_text(text)
        
        assert len(tokens) > 0
        assert "người" in [token.lower() for token in tokens]
        assert "lao động" in [token.lower() for token in tokens] or "lao_động" in [token.lower() for token in tokens]
        
        # Test empty text
        empty_tokens = await nlp_service.tokenize_text("")
        assert empty_tokens == []
    
    @pytest.mark.asyncio
    async def test_extract_keywords(self, nlp_service, sample_legal_text):
        """Test keyword extraction"""
        keywords = await nlp_service.extract_keywords(sample_legal_text)
        
        assert len(keywords) > 0
        assert any("lao động" in keyword.lower() for keyword in keywords)
        assert any("quyền" in keyword.lower() for keyword in keywords)
        
        # Test with limit
        limited_keywords = await nlp_service.extract_keywords(sample_legal_text, max_keywords=5)
        assert len(limited_keywords) <= 5
    
    @pytest.mark.asyncio
    async def test_named_entity_recognition(self, nlp_service, sample_legal_text):
        """Test named entity recognition"""
        entities = await nlp_service.named_entity_recognition(sample_legal_text)
        
        assert isinstance(entities, dict)
        
        # Should recognize legal entities
        if "ORGANIZATION" in entities:
            assert any("Việt Nam" in entity for entity in entities["ORGANIZATION"])
        
        if "LAW" in entities:
            assert len(entities["LAW"]) > 0
    
    @pytest.mark.asyncio
    async def test_extract_legal_terms(self, nlp_service, sample_legal_text):
        """Test legal term extraction"""
        legal_terms = await nlp_service.extract_legal_terms(sample_legal_text)
        
        assert isinstance(legal_terms, list)
        assert len(legal_terms) > 0
        
        # Should find common legal terms
        term_text = " ".join(legal_terms).lower()
        assert "quyền" in term_text or "nghĩa vụ" in term_text
    
    @pytest.mark.asyncio
    @patch('app.services.vietnamese_nlp.SentenceTransformer')
    async def test_generate_embedding(self, mock_transformer, nlp_service):
        """Test embedding generation"""
        # Mock the sentence transformer
        mock_model = Mock()
        mock_model.encode.return_value = np.array([0.1, 0.2, 0.3, 0.4, 0.5])
        mock_transformer.return_value = mock_model
        
        # Reinitialize service to use mock
        nlp_service.embedding_model = mock_model
        
        text = "Người lao động có quyền được bảo vệ."
        embedding = await nlp_service.generate_embedding(text)
        
        assert embedding is not None
        assert len(embedding) == 5
        assert isinstance(embedding, np.ndarray)
        
        # Test empty text
        empty_embedding = await nlp_service.generate_embedding("")
        assert empty_embedding is None
    
    @pytest.mark.asyncio
    async def test_preprocess_legal_document(self, nlp_service, sample_legal_text):
        """Test complete document preprocessing"""
        with patch.object(nlp_service, 'generate_embedding', return_value=np.array([0.1, 0.2, 0.3])):
            result = await nlp_service.preprocess_legal_document(sample_legal_text)
        
        assert isinstance(result, dict)
        assert "cleaned_text" in result
        assert "sentences" in result
        assert "legal_terms" in result
        assert "keywords" in result
        assert "entities" in result
        assert "embedding" in result
        assert "metadata" in result
        
        # Check metadata
        metadata = result["metadata"]
        assert "word_count" in metadata
        assert "sentence_count" in metadata
        assert "legal_term_count" in metadata
        assert metadata["word_count"] > 0
    
    @pytest.mark.asyncio
    async def test_calculate_similarity(self, nlp_service):
        """Test text similarity calculation"""
        text1 = "Người lao động có quyền được bảo vệ."
        text2 = "Quyền của người lao động được bảo vệ."
        text3 = "Thời tiết hôm nay rất đẹp."
        
        with patch.object(nlp_service, 'generate_embedding') as mock_embedding:
            # Mock embeddings
            mock_embedding.side_effect = [
                np.array([1.0, 0.0, 0.0]),  # text1
                np.array([0.8, 0.6, 0.0]),  # text2
                np.array([0.0, 0.0, 1.0])   # text3
            ]
            
            # Similar texts should have high similarity
            similarity_high = await nlp_service.calculate_similarity(text1, text2)
            assert similarity_high > 0.5
            
            # Different texts should have low similarity
            similarity_low = await nlp_service.calculate_similarity(text1, text3)
            assert similarity_low < 0.5
    
    @pytest.mark.asyncio
    async def test_detect_language(self, nlp_service):
        """Test language detection"""
        vietnamese_text = "Người lao động có quyền được bảo vệ."
        english_text = "Workers have the right to be protected."
        
        vi_lang = await nlp_service.detect_language(vietnamese_text)
        en_lang = await nlp_service.detect_language(english_text)
        
        assert vi_lang == "vi"
        assert en_lang == "en"
        
        # Test empty text
        empty_lang = await nlp_service.detect_language("")
        assert empty_lang == "unknown"
    
    @pytest.mark.asyncio
    async def test_error_handling(self, nlp_service):
        """Test error handling in NLP operations"""
        # Test with None input
        result = await nlp_service.clean_text(None)
        assert result == ""
        
        # Test with very long text
        very_long_text = "Điều 1. " * 10000
        result = await nlp_service.clean_text(very_long_text)
        assert len(result) > 0
        
        # Test with special characters
        special_text = "Điều 1. @#$%^&*()_+ Phạm vi điều chỉnh"
        result = await nlp_service.clean_text(special_text)
        assert "Điều 1." in result
        assert "Phạm vi điều chỉnh" in result


class TestLegalTerminologyIntegration:
    """Test integration with legal terminology service"""
    
    @pytest.mark.asyncio
    async def test_legal_term_extraction_integration(self):
        """Test integration between NLP and legal terminology services"""
        text = "Quốc hội ban hành Luật Lao động số 45/2019/QH14"
        
        # Extract legal terms using NLP service
        nlp_terms = await vietnamese_nlp.extract_legal_terms(text)
        
        # Extract using terminology service
        terminology_terms = legal_terminology.extract_legal_terms(text)
        
        # Both should find legal terms
        assert len(nlp_terms) > 0 or len(terminology_terms) > 0
        
        # Should find authority
        if terminology_terms.get("authority"):
            assert "Quốc hội" in terminology_terms["authority"]
    
    @pytest.mark.asyncio
    async def test_document_analysis_pipeline(self):
        """Test complete document analysis pipeline"""
        legal_document = """
        Luật số 45/2019/QH14
        LUẬT LAO ĐỘNG
        
        Điều 1. Phạm vi điều chỉnh
        Luật này quy định về quyền và nghĩa vụ của người lao động và người sử dụng lao động.
        """
        
        # Process with NLP service
        with patch.object(vietnamese_nlp, 'generate_embedding', return_value=np.array([0.1, 0.2, 0.3])):
            nlp_result = await vietnamese_nlp.preprocess_legal_document(legal_document)
        
        # Analyze with terminology service
        terminology_result = legal_terminology.analyze_legal_complexity(legal_document)
        
        # Both should provide meaningful results
        assert nlp_result["metadata"]["word_count"] > 0
        assert terminology_result.get("complexity_score", 0) > 0
        
        # Should identify document structure
        assert any("Điều" in sentence for sentence in nlp_result["sentences"])


class TestPerformance:
    """Performance tests for NLP operations"""
    
    @pytest.mark.asyncio
    async def test_processing_speed(self, nlp_service):
        """Test processing speed for various text sizes"""
        import time
        
        # Small text
        small_text = "Điều 1. Phạm vi điều chỉnh."
        start_time = time.time()
        await nlp_service.clean_text(small_text)
        small_time = time.time() - start_time
        
        # Medium text
        medium_text = "Điều 1. Phạm vi điều chỉnh. " * 100
        start_time = time.time()
        await nlp_service.clean_text(medium_text)
        medium_time = time.time() - start_time
        
        # Processing should be reasonably fast
        assert small_time < 1.0  # Less than 1 second
        assert medium_time < 5.0  # Less than 5 seconds
    
    @pytest.mark.asyncio
    async def test_concurrent_processing(self, nlp_service):
        """Test concurrent text processing"""
        texts = [
            "Điều 1. Phạm vi điều chỉnh.",
            "Điều 2. Đối tượng áp dụng.",
            "Điều 3. Giải thích từ ngữ.",
            "Điều 4. Nguyên tắc cơ bản.",
            "Điều 5. Chính sách của Nhà nước."
        ]
        
        # Process concurrently
        tasks = [nlp_service.clean_text(text) for text in texts]
        results = await asyncio.gather(*tasks)
        
        # All should complete successfully
        assert len(results) == len(texts)
        assert all(len(result) > 0 for result in results)


@pytest.mark.integration
class TestRealWorldScenarios:
    """Integration tests with real-world scenarios"""
    
    @pytest.mark.asyncio
    async def test_labor_law_processing(self):
        """Test processing of actual labor law content"""
        labor_law_text = """
        LUẬT LAO ĐỘNG
        Số: 45/2019/QH14
        
        Căn cứ Hiến pháp nước Cộng hòa xã hội chủ nghĩa Việt Nam;
        Quốc hội ban hành Luật Lao động.
        
        Chương I
        NHỮNG QUY ĐỊNH CHUNG
        
        Điều 1. Phạm vi điều chỉnh
        Luật này quy định về quyền và nghĩa vụ của người lao động và người sử dụng lao động trong quan hệ lao động; quản lý nhà nước về lao động; trách nhiệm của cơ quan, tổ chức, cá nhân có liên quan đến quan hệ lao động.
        """
        
        with patch.object(vietnamese_nlp, 'generate_embedding', return_value=np.array([0.1] * 768)):
            result = await vietnamese_nlp.preprocess_legal_document(labor_law_text)
        
        # Should extract meaningful information
        assert result["metadata"]["word_count"] > 50
        assert len(result["sentences"]) > 3
        assert len(result["legal_terms"]) > 0
        
        # Should identify key legal concepts
        legal_terms_text = " ".join(result["legal_terms"]).lower()
        assert any(term in legal_terms_text for term in ["quyền", "nghĩa vụ", "lao động"])
    
    @pytest.mark.asyncio
    async def test_contract_analysis(self):
        """Test analysis of contract text"""
        contract_text = """
        HỢP ĐỒNG LAO ĐỘNG
        
        Điều 1. Thông tin các bên
        - Bên A: Công ty TNHH ABC
        - Bên B: Ông/Bà XYZ
        
        Điều 2. Nội dung công việc
        Người lao động có trách nhiệm thực hiện các công việc được giao theo đúng quy định của pháp luật và nội quy công ty.
        
        Điều 3. Thời gian làm việc
        Thời gian làm việc không quá 8 giờ/ngày và 48 giờ/tuần theo quy định của Bộ luật Lao động.
        """
        
        # Analyze contract complexity
        complexity = legal_terminology.analyze_legal_complexity(contract_text)
        
        # Should identify contract structure
        assert complexity["legal_term_count"] > 0
        assert complexity["complexity_score"] > 0
        
        # Should find references to labor law
        references = legal_terminology.extract_document_references(contract_text)
        assert len(references) > 0 or "Bộ luật Lao động" in contract_text


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
