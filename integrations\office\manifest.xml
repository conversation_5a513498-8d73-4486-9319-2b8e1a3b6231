<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp xmlns="http://schemas.microsoft.com/office/appforoffice/1.1" 
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
           xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0" 
           xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides" 
           xsi:type="TaskPaneApp">

  <!-- Begin Basic Settings: Add-in metadata, used for all versions of Office unless override provided. -->
  <Id>12345678-1234-1234-1234-123456789012</Id>
  <Version>*******</Version>
  <ProviderName>VietLaw.AI</ProviderName>
  <DefaultLocale>vi-VN</DefaultLocale>
  
  <!-- The display name of your add-in. Used on the store and various places of the Office UI such as the add-ins dialog. -->
  <DisplayName DefaultValue="VietLaw.AI - Vietnamese Legal Assistant" />
  <Description DefaultValue="AI-powered Vietnamese legal document analysis and search assistant for Microsoft Office applications." />
  
  <!-- Icon for your add-in. Used on installation screens and the add-ins dialog. -->
  <IconUrl DefaultValue="https://vietlaw.ai/assets/icon-32.png" />
  <HighResolutionIconUrl DefaultValue="https://vietlaw.ai/assets/icon-64.png" />
  
  <!-- SupportUrl is used by the Office Store to provide a way for users to get support for your add-in. -->
  <SupportUrl DefaultValue="https://vietlaw.ai/support" />
  
  <!-- Domains that will be allowed when navigating. For example, if you use ShowTaskpane and then have an href link, navigation will only be allowed if the domain is on this list. -->
  <AppDomains>
    <AppDomain>https://vietlaw.ai</AppDomain>
    <AppDomain>https://api.vietlaw.ai</AppDomain>
  </AppDomains>
  
  <!-- End Basic Settings. -->

  <!-- Begin TaskPane Mode integration. This section is used if there are no VersionOverrides or if the Office client version does not support add-in commands. -->
  <Hosts>
    <Host Name="Document" />
    <Host Name="Workbook" />
    <Host Name="Presentation" />
  </Hosts>
  
  <DefaultSettings>
    <SourceLocation DefaultValue="https://vietlaw.ai/office-addin/taskpane.html" />
  </DefaultSettings>
  
  <!-- End TaskPane Mode integration. -->

  <Permissions>ReadWriteDocument</Permissions>

  <!-- Begin Add-in Commands Mode integration. -->
  <VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="VersionOverridesV1_0">

    <!-- The Hosts node is required. -->
    <Hosts>
      <!-- Each host can have a different set of commands. -->
      <!-- Excel host is Workbook, Word host is Document, and PowerPoint host is Presentation. -->
      <!-- Make sure the hosts you override match the hosts declared in the top level section. -->
      <Host xsi:type="Document">
        <!-- Form factor. Currently only DesktopFormFactor is supported. -->
        <DesktopFormFactor>
          <!-- Function file is a HTML page that includes the JavaScript where functions for ExecuteAction will be called. 
               Think of the FunctionFile as the code behind ExecuteFunction. -->
          <FunctionFile resid="VietLawAI.DesktopFunctionFile.Url" />

          <!-- PrimaryCommandSurface is the main Office Ribbon. -->
          <ExtensionPoint xsi:type="PrimaryCommandSurface">
            <!-- Use OfficeTab to extend an existing Tab. Use CustomTab to create a new tab. -->
            <CustomTab id="VietLawAI.Tab">
              <Group id="VietLawAI.Group1">
                <Label resid="VietLawAI.Group1Label" />
                <Icon>
                  <bt:Image size="16" resid="VietLawAI.tpicon_16x16" />
                  <bt:Image size="32" resid="VietLawAI.tpicon_32x32" />
                  <bt:Image size="80" resid="VietLawAI.tpicon_80x80" />
                </Icon>

                <!-- Control. It can be of type "Button" or "Menu". -->
                <Control xsi:type="Button" id="VietLawAI.TaskpaneButton">
                  <Label resid="VietLawAI.TaskpaneButton.Label" />
                  <Supertip>
                    <Title resid="VietLawAI.TaskpaneButton.Label" />
                    <Description resid="VietLawAI.TaskpaneButton.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="VietLawAI.tpicon_16x16" />
                    <bt:Image size="32" resid="VietLawAI.tpicon_32x32" />
                    <bt:Image size="80" resid="VietLawAI.tpicon_80x80" />
                  </Icon>

                  <!-- This is what happens when the command is triggered (E.g. click on the Ribbon). Supported actions are ExecuteFunction or ShowTaskpane. -->
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>ButtonId1</TaskpaneId>
                    <!-- Provide a url resource id for the location that will be displayed on the task pane. -->
                    <SourceLocation resid="VietLawAI.Taskpane.Url" />
                  </Action>
                </Control>

                <Control xsi:type="Button" id="VietLawAI.AnalyzeButton">
                  <Label resid="VietLawAI.AnalyzeButton.Label" />
                  <Supertip>
                    <Title resid="VietLawAI.AnalyzeButton.Label" />
                    <Description resid="VietLawAI.AnalyzeButton.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="VietLawAI.analyze_16x16" />
                    <bt:Image size="32" resid="VietLawAI.analyze_32x32" />
                    <bt:Image size="80" resid="VietLawAI.analyze_80x80" />
                  </Icon>
                  <Action xsi:type="ExecuteFunction">
                    <FunctionName>analyzeSelectedText</FunctionName>
                  </Action>
                </Control>

                <Control xsi:type="Button" id="VietLawAI.SearchButton">
                  <Label resid="VietLawAI.SearchButton.Label" />
                  <Supertip>
                    <Title resid="VietLawAI.SearchButton.Label" />
                    <Description resid="VietLawAI.SearchButton.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="VietLawAI.search_16x16" />
                    <bt:Image size="32" resid="VietLawAI.search_32x32" />
                    <bt:Image size="80" resid="VietLawAI.search_80x80" />
                  </Icon>
                  <Action xsi:type="ExecuteFunction">
                    <FunctionName>searchLegalDocuments</FunctionName>
                  </Action>
                </Control>

              </Group>
              <Label resid="VietLawAI.TabLabel" />
            </CustomTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>

      <Host xsi:type="Workbook">
        <DesktopFormFactor>
          <FunctionFile resid="VietLawAI.DesktopFunctionFile.Url" />
          <ExtensionPoint xsi:type="PrimaryCommandSurface">
            <CustomTab id="VietLawAI.Tab">
              <Group id="VietLawAI.Group1">
                <Label resid="VietLawAI.Group1Label" />
                <Icon>
                  <bt:Image size="16" resid="VietLawAI.tpicon_16x16" />
                  <bt:Image size="32" resid="VietLawAI.tpicon_32x32" />
                  <bt:Image size="80" resid="VietLawAI.tpicon_80x80" />
                </Icon>

                <Control xsi:type="Button" id="VietLawAI.TaskpaneButton">
                  <Label resid="VietLawAI.TaskpaneButton.Label" />
                  <Supertip>
                    <Title resid="VietLawAI.TaskpaneButton.Label" />
                    <Description resid="VietLawAI.TaskpaneButton.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="VietLawAI.tpicon_16x16" />
                    <bt:Image size="32" resid="VietLawAI.tpicon_32x32" />
                    <bt:Image size="80" resid="VietLawAI.tpicon_80x80" />
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>ButtonId1</TaskpaneId>
                    <SourceLocation resid="VietLawAI.Taskpane.Url" />
                  </Action>
                </Control>

                <Control xsi:type="Button" id="VietLawAI.ComplianceButton">
                  <Label resid="VietLawAI.ComplianceButton.Label" />
                  <Supertip>
                    <Title resid="VietLawAI.ComplianceButton.Label" />
                    <Description resid="VietLawAI.ComplianceButton.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="VietLawAI.compliance_16x16" />
                    <bt:Image size="32" resid="VietLawAI.compliance_32x32" />
                    <bt:Image size="80" resid="VietLawAI.compliance_80x80" />
                  </Icon>
                  <Action xsi:type="ExecuteFunction">
                    <FunctionName>checkCompliance</FunctionName>
                  </Action>
                </Control>

              </Group>
              <Label resid="VietLawAI.TabLabel" />
            </CustomTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>

      <Host xsi:type="Presentation">
        <DesktopFormFactor>
          <FunctionFile resid="VietLawAI.DesktopFunctionFile.Url" />
          <ExtensionPoint xsi:type="PrimaryCommandSurface">
            <CustomTab id="VietLawAI.Tab">
              <Group id="VietLawAI.Group1">
                <Label resid="VietLawAI.Group1Label" />
                <Icon>
                  <bt:Image size="16" resid="VietLawAI.tpicon_16x16" />
                  <bt:Image size="32" resid="VietLawAI.tpicon_32x32" />
                  <bt:Image size="80" resid="VietLawAI.tpicon_80x80" />
                </Icon>

                <Control xsi:type="Button" id="VietLawAI.TaskpaneButton">
                  <Label resid="VietLawAI.TaskpaneButton.Label" />
                  <Supertip>
                    <Title resid="VietLawAI.TaskpaneButton.Label" />
                    <Description resid="VietLawAI.TaskpaneButton.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="VietLawAI.tpicon_16x16" />
                    <bt:Image size="32" resid="VietLawAI.tpicon_32x32" />
                    <bt:Image size="80" resid="VietLawAI.tpicon_80x80" />
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>ButtonId1</TaskpaneId>
                    <SourceLocation resid="VietLawAI.Taskpane.Url" />
                  </Action>
                </Control>

              </Group>
              <Label resid="VietLawAI.TabLabel" />
            </CustomTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>
    </Hosts>

    <!-- You can use resources across hosts and form factors. -->
    <Resources>
      <bt:Images>
        <bt:Image id="VietLawAI.tpicon_16x16" DefaultValue="https://vietlaw.ai/assets/icon-16.png" />
        <bt:Image id="VietLawAI.tpicon_32x32" DefaultValue="https://vietlaw.ai/assets/icon-32.png" />
        <bt:Image id="VietLawAI.tpicon_80x80" DefaultValue="https://vietlaw.ai/assets/icon-80.png" />
        <bt:Image id="VietLawAI.analyze_16x16" DefaultValue="https://vietlaw.ai/assets/analyze-16.png" />
        <bt:Image id="VietLawAI.analyze_32x32" DefaultValue="https://vietlaw.ai/assets/analyze-32.png" />
        <bt:Image id="VietLawAI.analyze_80x80" DefaultValue="https://vietlaw.ai/assets/analyze-80.png" />
        <bt:Image id="VietLawAI.search_16x16" DefaultValue="https://vietlaw.ai/assets/search-16.png" />
        <bt:Image id="VietLawAI.search_32x32" DefaultValue="https://vietlaw.ai/assets/search-32.png" />
        <bt:Image id="VietLawAI.search_80x80" DefaultValue="https://vietlaw.ai/assets/search-80.png" />
        <bt:Image id="VietLawAI.compliance_16x16" DefaultValue="https://vietlaw.ai/assets/compliance-16.png" />
        <bt:Image id="VietLawAI.compliance_32x32" DefaultValue="https://vietlaw.ai/assets/compliance-32.png" />
        <bt:Image id="VietLawAI.compliance_80x80" DefaultValue="https://vietlaw.ai/assets/compliance-80.png" />
      </bt:Images>
      <bt:Urls>
        <bt:Url id="VietLawAI.Taskpane.Url" DefaultValue="https://vietlaw.ai/office-addin/taskpane.html" />
        <bt:Url id="VietLawAI.DesktopFunctionFile.Url" DefaultValue="https://vietlaw.ai/office-addin/functions.html" />
      </bt:Urls>
      <!-- ShortStrings max characters==125. -->
      <bt:ShortStrings>
        <bt:String id="VietLawAI.TaskpaneButton.Label" DefaultValue="VietLaw.AI" />
        <bt:String id="VietLawAI.AnalyzeButton.Label" DefaultValue="Phân tích văn bản" />
        <bt:String id="VietLawAI.SearchButton.Label" DefaultValue="Tìm kiếm pháp luật" />
        <bt:String id="VietLawAI.ComplianceButton.Label" DefaultValue="Kiểm tra tuân thủ" />
        <bt:String id="VietLawAI.Group1Label" DefaultValue="VietLaw.AI Tools" />
        <bt:String id="VietLawAI.TabLabel" DefaultValue="VietLaw.AI" />
      </bt:ShortStrings>
      <!-- LongStrings max characters==250. -->
      <bt:LongStrings>
        <bt:String id="VietLawAI.TaskpaneButton.Tooltip" DefaultValue="Mở bảng điều khiển VietLaw.AI để tìm kiếm và phân tích văn bản pháp luật Việt Nam" />
        <bt:String id="VietLawAI.AnalyzeButton.Tooltip" DefaultValue="Phân tích văn bản được chọn để đánh giá rủi ro pháp lý và tuân thủ" />
        <bt:String id="VietLawAI.SearchButton.Tooltip" DefaultValue="Tìm kiếm văn bản pháp luật liên quan đến nội dung được chọn" />
        <bt:String id="VietLawAI.ComplianceButton.Tooltip" DefaultValue="Kiểm tra tính tuân thủ của dữ liệu trong bảng tính với quy định pháp luật" />
      </bt:LongStrings>
    </Resources>
  </VersionOverrides>
  <!-- End Add-in Commands Mode integration. -->

</OfficeApp>
