@echo off
REM VietLaw.AI Setuptools Error Fix Script for Windows
REM Fixes the "Cannot import 'setuptools.build_meta'" error

setlocal enabledelayedexpansion

REM Configuration
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set BACKEND_DIR=%PROJECT_ROOT%\backend

REM Colors
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%========================================%NC%
echo %BLUE%VietLaw.AI Setuptools Error Fix%NC%
echo %BLUE%========================================%NC%
echo.

echo %YELLOW%[INFO]%NC% This script will fix the setuptools.build_meta error
echo %YELLOW%[INFO]%NC% by recreating the Python virtual environment
echo.

REM Check if we're in the right directory
if not exist "%BACKEND_DIR%" (
    echo %RED%[ERROR]%NC% Backend directory not found: %BACKEND_DIR%
    echo %RED%[ERROR]%NC% Please run this script from the project root
    pause
    exit /b 1
)

cd /d "%BACKEND_DIR%"

echo %BLUE%[STEP 1]%NC% Removing corrupted virtual environment...
if exist "venv" (
    echo %YELLOW%[INFO]%NC% Removing existing venv directory...
    rmdir /s /q venv
    if errorlevel 1 (
        echo %RED%[ERROR]%NC% Failed to remove venv directory
        echo %YELLOW%[INFO]%NC% Please close any applications using the venv and try again
        pause
        exit /b 1
    )
    echo %GREEN%[SUCCESS]%NC% Old virtual environment removed
) else (
    echo %YELLOW%[INFO]%NC% No existing virtual environment found
)

echo.
echo %BLUE%[STEP 2]%NC% Creating fresh virtual environment...
python -m venv venv
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to create virtual environment
    echo %YELLOW%[INFO]%NC% Make sure Python 3.9+ is installed and accessible
    pause
    exit /b 1
)
echo %GREEN%[SUCCESS]%NC% Virtual environment created

echo.
echo %BLUE%[STEP 3]%NC% Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to activate virtual environment
    pause
    exit /b 1
)
echo %GREEN%[SUCCESS]%NC% Virtual environment activated

echo.
echo %BLUE%[STEP 4]%NC% Installing essential build tools...
echo %YELLOW%[INFO]%NC% Installing pip, setuptools, and wheel...

REM Install build tools first with specific versions that work well together
python -m pip install --upgrade pip>=23.0
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to upgrade pip
    pause
    exit /b 1
)

pip install --upgrade setuptools>=65.0 wheel>=0.38.0
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to install setuptools and wheel
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Build tools installed successfully

echo.
echo %BLUE%[STEP 5]%NC% Installing PyTorch (CPU version for compatibility)...
pip install torch>=2.2.0,^<2.8.0 --index-url https://download.pytorch.org/whl/cpu
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to install PyTorch
    echo %YELLOW%[INFO]%NC% Trying alternative installation...
    pip install torch --index-url https://download.pytorch.org/whl/cpu
    if errorlevel 1 (
        echo %RED%[ERROR]%NC% PyTorch installation failed completely
        pause
        exit /b 1
    )
)
echo %GREEN%[SUCCESS]%NC% PyTorch installed successfully

echo.
echo %BLUE%[STEP 6]%NC% Installing other dependencies...
echo %YELLOW%[INFO]%NC% This may take a few minutes...

REM Install dependencies one by one to catch specific errors
pip install fastapi uvicorn pydantic sqlalchemy alembic
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to install core web framework dependencies
    pause
    exit /b 1
)

pip install psycopg2-binary asyncpg pgvector redis aioredis
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to install database dependencies
    pause
    exit /b 1
)

pip install sentence-transformers transformers numpy scikit-learn pandas
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to install ML dependencies
    pause
    exit /b 1
)

pip install underthesea pyvi
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to install Vietnamese NLP dependencies
    pause
    exit /b 1
)

REM Install remaining dependencies
pip install -r requirements.txt
if errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% Some dependencies from requirements.txt failed
    echo %YELLOW%[INFO]%NC% But core dependencies are installed
)

echo %GREEN%[SUCCESS]%NC% Dependencies installed successfully

echo.
echo %BLUE%[STEP 7]%NC% Verifying installation...
python -c "import fastapi, sqlalchemy, torch, sentence_transformers, underthesea; print('All core dependencies verified!')"
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Verification failed
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Installation verified successfully!

echo.
echo %GREEN%========================================%NC%
echo %GREEN%SETUPTOOLS ERROR FIXED SUCCESSFULLY!%NC%
echo %GREEN%========================================%NC%
echo.
echo %BLUE%Next steps:%NC%
echo 1. The virtual environment is now ready
echo 2. To activate it: cd backend ^&^& venv\Scripts\activate
echo 3. To start the backend: uvicorn main:app --reload
echo 4. To install frontend: cd frontend ^&^& npm install --legacy-peer-deps
echo.
echo %YELLOW%[INFO]%NC% If you encounter any issues, run this script again
echo.
pause
