#!/usr/bin/env python3
"""
VietLaw.AI Dependency Verification Script
Checks if all required dependencies are properly installed
"""

import sys
import subprocess
import json
import os
from pathlib import Path

# Colors for output
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    ENDC = '\033[0m'

def log_info(message):
    print(f"{Colors.BLUE}[INFO]{Colors.ENDC} {message}")

def log_success(message):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.ENDC} {message}")

def log_warning(message):
    print(f"{Colors.YELLOW}[WARNING]{Colors.ENDC} {message}")

def log_error(message):
    print(f"{Colors.RED}[ERROR]{Colors.ENDC} {message}")

def check_python_version():
    """Check Python version"""
    log_info("Checking Python version...")
    
    if sys.version_info < (3, 9):
        log_error(f"Python 3.9+ required. Current: {sys.version}")
        return False
    
    log_success(f"Python version: {sys.version.split()[0]}")
    return True

def check_node_version():
    """Check Node.js version"""
    log_info("Checking Node.js version...")
    
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode != 0:
            log_error("Node.js not found")
            return False
        
        version = result.stdout.strip().replace('v', '')
        major_version = int(version.split('.')[0])
        
        if major_version < 18:
            log_error(f"Node.js 18+ required. Current: {version}")
            return False
        
        log_success(f"Node.js version: {version}")
        return True
    
    except Exception as e:
        log_error(f"Error checking Node.js: {e}")
        return False

def check_backend_dependencies():
    """Check backend Python dependencies"""
    log_info("Checking backend dependencies...")
    
    required_packages = [
        'fastapi',
        'sqlalchemy',
        'torch',
        'sentence_transformers',
        'underthesea',
        'psycopg2',
        'redis',
        'uvicorn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            log_success(f"✓ {package}")
        except ImportError:
            log_error(f"✗ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        log_error(f"Missing packages: {', '.join(missing_packages)}")
        return False
    
    log_success("All backend dependencies are installed")
    return True

def check_torch_version():
    """Check PyTorch version specifically"""
    log_info("Checking PyTorch version...")
    
    try:
        import torch
        version = torch.__version__
        log_success(f"PyTorch version: {version}")
        
        # Check if CUDA is available (optional)
        if torch.cuda.is_available():
            log_info(f"CUDA available: {torch.cuda.get_device_name(0)}")
        else:
            log_info("CUDA not available (using CPU)")
        
        return True
    
    except ImportError:
        log_error("PyTorch not installed")
        return False

def check_frontend_dependencies():
    """Check frontend Node.js dependencies"""
    log_info("Checking frontend dependencies...")
    
    frontend_dir = Path(__file__).parent.parent / 'frontend'
    package_json_path = frontend_dir / 'package.json'
    node_modules_path = frontend_dir / 'node_modules'
    
    if not package_json_path.exists():
        log_error("package.json not found in frontend directory")
        return False
    
    if not node_modules_path.exists():
        log_error("node_modules not found. Run 'npm install' in frontend directory")
        return False
    
    # Check if key packages are installed
    key_packages = [
        'react',
        'react-dom',
        'typescript',
        'vite',
        '@tanstack/react-query',
        'zustand',
        'tailwindcss'
    ]
    
    missing_packages = []
    
    for package in key_packages:
        package_path = node_modules_path / package
        if not package_path.exists():
            missing_packages.append(package)
            log_error(f"✗ {package}")
        else:
            log_success(f"✓ {package}")
    
    if missing_packages:
        log_error(f"Missing packages: {', '.join(missing_packages)}")
        return False
    
    log_success("All frontend dependencies are installed")
    return True

def check_database_requirements():
    """Check database requirements"""
    log_info("Checking database requirements...")
    
    try:
        import psycopg2
        log_success("✓ PostgreSQL driver (psycopg2)")
    except ImportError:
        log_error("✗ PostgreSQL driver (psycopg2)")
        return False
    
    try:
        import redis
        log_success("✓ Redis client")
    except ImportError:
        log_error("✗ Redis client")
        return False
    
    return True

def check_ai_ml_requirements():
    """Check AI/ML requirements"""
    log_info("Checking AI/ML requirements...")
    
    try:
        import sentence_transformers
        log_success("✓ Sentence Transformers")
    except ImportError:
        log_error("✗ Sentence Transformers")
        return False
    
    try:
        import underthesea
        log_success("✓ Underthesea (Vietnamese NLP)")
    except ImportError:
        log_error("✗ Underthesea (Vietnamese NLP)")
        return False
    
    try:
        import numpy
        log_success("✓ NumPy")
    except ImportError:
        log_error("✗ NumPy")
        return False
    
    return True

def run_quick_tests():
    """Run quick functionality tests"""
    log_info("Running quick functionality tests...")
    
    # Test Vietnamese text processing
    try:
        from underthesea import word_tokenize
        test_text = "Luật Lao động quy định quyền và nghĩa vụ của người lao động"
        tokens = word_tokenize(test_text)
        log_success(f"✓ Vietnamese tokenization: {len(tokens)} tokens")
    except Exception as e:
        log_error(f"✗ Vietnamese tokenization failed: {e}")
        return False
    
    # Test sentence transformers
    try:
        from sentence_transformers import SentenceTransformer
        # Use a lightweight model for testing
        model = SentenceTransformer('all-MiniLM-L6-v2')
        embedding = model.encode("test sentence")
        log_success(f"✓ Sentence embedding: {embedding.shape}")
    except Exception as e:
        log_error(f"✗ Sentence embedding failed: {e}")
        return False
    
    return True

def main():
    """Main verification function"""
    print("=" * 60)
    print("VietLaw.AI Dependency Verification")
    print("=" * 60)
    
    checks = [
        ("Python Version", check_python_version),
        ("Node.js Version", check_node_version),
        ("Backend Dependencies", check_backend_dependencies),
        ("PyTorch Version", check_torch_version),
        ("Frontend Dependencies", check_frontend_dependencies),
        ("Database Requirements", check_database_requirements),
        ("AI/ML Requirements", check_ai_ml_requirements),
        ("Quick Functionality Tests", run_quick_tests)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n{'-' * 40}")
        print(f"Checking: {check_name}")
        print(f"{'-' * 40}")
        
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            log_error(f"Error during {check_name}: {e}")
            results.append((check_name, False))
    
    # Summary
    print(f"\n{'=' * 60}")
    print("VERIFICATION SUMMARY")
    print(f"{'=' * 60}")
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "PASS" if result else "FAIL"
        color = Colors.GREEN if result else Colors.RED
        print(f"{color}{status:>6}{Colors.ENDC} | {check_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} checks passed")
    
    if passed == total:
        log_success("All dependency checks passed! VietLaw.AI is ready to run.")
        return 0
    else:
        log_error(f"{total - passed} checks failed. Please fix the issues above.")
        print("\nTroubleshooting:")
        print("1. Run the installation script: ./scripts/install-dependencies.sh")
        print("2. Check the README.md for manual installation steps")
        print("3. Fix common issues: ./scripts/install-dependencies.sh --fix-issues")
        return 1

if __name__ == "__main__":
    sys.exit(main())
