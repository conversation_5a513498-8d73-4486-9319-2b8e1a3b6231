"""
VietLaw.AI Single Sign-On (SSO) Service
Enterprise SSO integration with SAML, OAuth, and OIDC
"""

import logging
import jwt
import base64
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from urllib.parse import urlencode, parse_qs
import httpx
from cryptography.x509 import load_pem_x509_certificate
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding

from app.core.config import settings
from app.models.user import User
from app.core.database import get_db_session_context

logger = logging.getLogger(__name__)


class SSOProvider:
    """Base SSO provider class"""
    
    def __init__(self, provider_config: Dict):
        self.config = provider_config
        self.provider_name = provider_config.get("name")
        self.provider_type = provider_config.get("type")  # saml, oauth, oidc
    
    async def authenticate(self, request_data: Dict) -> Optional[Dict]:
        """Authenticate user with SSO provider"""
        raise NotImplementedError
    
    async def get_user_info(self, token: str) -> Optional[Dict]:
        """Get user information from SSO provider"""
        raise NotImplementedError


class SAMLProvider(SSOProvider):
    """SAML 2.0 SSO Provider"""
    
    def __init__(self, provider_config: Dict):
        super().__init__(provider_config)
        self.entity_id = provider_config.get("entity_id")
        self.sso_url = provider_config.get("sso_url")
        self.sls_url = provider_config.get("sls_url")
        self.x509_cert = provider_config.get("x509_cert")
        self.private_key = provider_config.get("private_key")
    
    def generate_saml_request(self, relay_state: Optional[str] = None) -> str:
        """Generate SAML authentication request"""
        try:
            request_id = f"_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
            issue_instant = datetime.utcnow().isoformat() + "Z"
            
            saml_request = f"""
            <samlp:AuthnRequest 
                xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
                xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
                ID="{request_id}"
                Version="2.0"
                IssueInstant="{issue_instant}"
                Destination="{self.sso_url}"
                AssertionConsumerServiceURL="{settings.BASE_URL}/auth/saml/acs"
                ProtocolBinding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST">
                <saml:Issuer>{settings.BASE_URL}</saml:Issuer>
                <samlp:NameIDPolicy Format="urn:oasis:names:tc:SAML:2.0:nameid-format:emailAddress" AllowCreate="true"/>
            </samlp:AuthnRequest>
            """
            
            # Encode and compress
            encoded_request = base64.b64encode(saml_request.encode()).decode()
            
            # Build redirect URL
            params = {
                "SAMLRequest": encoded_request,
                "RelayState": relay_state or ""
            }
            
            return f"{self.sso_url}?{urlencode(params)}"
            
        except Exception as e:
            logger.error(f"Error generating SAML request: {e}")
            return None
    
    async def validate_saml_response(self, saml_response: str) -> Optional[Dict]:
        """Validate SAML response and extract user info"""
        try:
            # Decode SAML response
            decoded_response = base64.b64decode(saml_response).decode()
            
            # Parse XML
            root = ET.fromstring(decoded_response)
            
            # Extract assertion
            assertion = root.find(".//{urn:oasis:names:tc:SAML:2.0:assertion}Assertion")
            if assertion is None:
                logger.error("No assertion found in SAML response")
                return None
            
            # Validate signature (simplified - in production, use proper SAML library)
            if not self._validate_signature(assertion):
                logger.error("Invalid SAML signature")
                return None
            
            # Extract user attributes
            user_info = self._extract_user_attributes(assertion)
            
            return user_info
            
        except Exception as e:
            logger.error(f"Error validating SAML response: {e}")
            return None
    
    def _validate_signature(self, assertion) -> bool:
        """Validate SAML assertion signature"""
        # Simplified signature validation
        # In production, use proper SAML library like python3-saml
        return True
    
    def _extract_user_attributes(self, assertion) -> Dict:
        """Extract user attributes from SAML assertion"""
        try:
            user_info = {}
            
            # Extract NameID
            name_id = assertion.find(".//{urn:oasis:names:tc:SAML:2.0:assertion}NameID")
            if name_id is not None:
                user_info["email"] = name_id.text
            
            # Extract attributes
            attr_statement = assertion.find(".//{urn:oasis:names:tc:SAML:2.0:assertion}AttributeStatement")
            if attr_statement is not None:
                for attr in attr_statement.findall(".//{urn:oasis:names:tc:SAML:2.0:assertion}Attribute"):
                    attr_name = attr.get("Name")
                    attr_value = attr.find(".//{urn:oasis:names:tc:SAML:2.0:assertion}AttributeValue")
                    
                    if attr_value is not None:
                        if attr_name == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname":
                            user_info["first_name"] = attr_value.text
                        elif attr_name == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname":
                            user_info["last_name"] = attr_value.text
                        elif attr_name == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress":
                            user_info["email"] = attr_value.text
                        elif attr_name == "http://schemas.microsoft.com/ws/2008/06/identity/claims/groups":
                            user_info["groups"] = attr_value.text.split(",") if attr_value.text else []
            
            return user_info
            
        except Exception as e:
            logger.error(f"Error extracting user attributes: {e}")
            return {}


class OAuthProvider(SSOProvider):
    """OAuth 2.0 SSO Provider"""
    
    def __init__(self, provider_config: Dict):
        super().__init__(provider_config)
        self.client_id = provider_config.get("client_id")
        self.client_secret = provider_config.get("client_secret")
        self.auth_url = provider_config.get("auth_url")
        self.token_url = provider_config.get("token_url")
        self.user_info_url = provider_config.get("user_info_url")
        self.scope = provider_config.get("scope", "openid email profile")
    
    def get_authorization_url(self, state: Optional[str] = None) -> str:
        """Generate OAuth authorization URL"""
        try:
            params = {
                "client_id": self.client_id,
                "response_type": "code",
                "scope": self.scope,
                "redirect_uri": f"{settings.BASE_URL}/auth/oauth/callback",
                "state": state or ""
            }
            
            return f"{self.auth_url}?{urlencode(params)}"
            
        except Exception as e:
            logger.error(f"Error generating OAuth URL: {e}")
            return None
    
    async def exchange_code_for_token(self, code: str) -> Optional[Dict]:
        """Exchange authorization code for access token"""
        try:
            async with httpx.AsyncClient() as client:
                data = {
                    "grant_type": "authorization_code",
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "code": code,
                    "redirect_uri": f"{settings.BASE_URL}/auth/oauth/callback"
                }
                
                response = await client.post(self.token_url, data=data)
                response.raise_for_status()
                
                return response.json()
                
        except Exception as e:
            logger.error(f"Error exchanging OAuth code: {e}")
            return None
    
    async def get_user_info(self, access_token: str) -> Optional[Dict]:
        """Get user information using access token"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {access_token}"}
                response = await client.get(self.user_info_url, headers=headers)
                response.raise_for_status()
                
                return response.json()
                
        except Exception as e:
            logger.error(f"Error getting OAuth user info: {e}")
            return None


class OIDCProvider(OAuthProvider):
    """OpenID Connect SSO Provider"""
    
    def __init__(self, provider_config: Dict):
        super().__init__(provider_config)
        self.discovery_url = provider_config.get("discovery_url")
        self.jwks_uri = provider_config.get("jwks_uri")
        self._discovery_doc = None
        self._jwks = None
    
    async def get_discovery_document(self) -> Optional[Dict]:
        """Get OIDC discovery document"""
        if self._discovery_doc:
            return self._discovery_doc
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.discovery_url)
                response.raise_for_status()
                
                self._discovery_doc = response.json()
                return self._discovery_doc
                
        except Exception as e:
            logger.error(f"Error getting OIDC discovery document: {e}")
            return None
    
    async def validate_id_token(self, id_token: str) -> Optional[Dict]:
        """Validate OIDC ID token"""
        try:
            # Get JWKS for token validation
            if not self._jwks:
                await self._get_jwks()
            
            # Decode token header to get key ID
            header = jwt.get_unverified_header(id_token)
            kid = header.get("kid")
            
            # Find matching key
            key = None
            for jwk in self._jwks.get("keys", []):
                if jwk.get("kid") == kid:
                    key = jwt.algorithms.RSAAlgorithm.from_jwk(jwk)
                    break
            
            if not key:
                logger.error("No matching key found for ID token")
                return None
            
            # Validate and decode token
            payload = jwt.decode(
                id_token,
                key,
                algorithms=["RS256"],
                audience=self.client_id,
                options={"verify_exp": True}
            )
            
            return payload
            
        except Exception as e:
            logger.error(f"Error validating ID token: {e}")
            return None
    
    async def _get_jwks(self):
        """Get JSON Web Key Set"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.jwks_uri)
                response.raise_for_status()
                
                self._jwks = response.json()
                
        except Exception as e:
            logger.error(f"Error getting JWKS: {e}")


class SSOService:
    """Main SSO service for managing multiple providers"""
    
    def __init__(self):
        self.providers: Dict[str, SSOProvider] = {}
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize SSO providers from configuration"""
        try:
            sso_config = getattr(settings, "SSO_PROVIDERS", {})
            
            for provider_name, config in sso_config.items():
                provider_type = config.get("type")
                
                if provider_type == "saml":
                    provider = SAMLProvider(config)
                elif provider_type == "oauth":
                    provider = OAuthProvider(config)
                elif provider_type == "oidc":
                    provider = OIDCProvider(config)
                else:
                    logger.warning(f"Unknown SSO provider type: {provider_type}")
                    continue
                
                self.providers[provider_name] = provider
                logger.info(f"Initialized SSO provider: {provider_name}")
                
        except Exception as e:
            logger.error(f"Error initializing SSO providers: {e}")
    
    def get_provider(self, provider_name: str) -> Optional[SSOProvider]:
        """Get SSO provider by name"""
        return self.providers.get(provider_name)
    
    def list_providers(self) -> List[Dict]:
        """List available SSO providers"""
        return [
            {
                "name": name,
                "type": provider.provider_type,
                "display_name": provider.config.get("display_name", name)
            }
            for name, provider in self.providers.items()
        ]
    
    async def authenticate_user(
        self,
        provider_name: str,
        auth_data: Dict
    ) -> Optional[User]:
        """Authenticate user with SSO provider"""
        try:
            provider = self.get_provider(provider_name)
            if not provider:
                logger.error(f"SSO provider not found: {provider_name}")
                return None
            
            # Get user info from provider
            user_info = await provider.authenticate(auth_data)
            if not user_info:
                logger.error(f"Authentication failed with provider: {provider_name}")
                return None
            
            # Find or create user
            user = await self._find_or_create_user(user_info, provider_name)
            
            return user
            
        except Exception as e:
            logger.error(f"Error authenticating user with SSO: {e}")
            return None
    
    async def _find_or_create_user(
        self,
        user_info: Dict,
        provider_name: str
    ) -> Optional[User]:
        """Find existing user or create new one"""
        try:
            email = user_info.get("email")
            if not email:
                logger.error("No email found in SSO user info")
                return None
            
            async with get_db_session_context() as db:
                # Try to find existing user
                from sqlalchemy import select
                result = await db.execute(
                    select(User).where(User.email == email)
                )
                user = result.scalar_one_or_none()
                
                if user:
                    # Update user info from SSO
                    if user_info.get("first_name"):
                        user.first_name = user_info["first_name"]
                    if user_info.get("last_name"):
                        user.last_name = user_info["last_name"]
                    
                    # Update SSO info
                    user.sso_provider = provider_name
                    user.last_login = datetime.utcnow()
                    
                    await db.commit()
                    return user
                else:
                    # Create new user
                    user = User(
                        email=email,
                        first_name=user_info.get("first_name", ""),
                        last_name=user_info.get("last_name", ""),
                        is_active=True,
                        is_verified=True,  # SSO users are pre-verified
                        sso_provider=provider_name,
                        subscription_plan="basic",  # Default plan
                        last_login=datetime.utcnow()
                    )
                    
                    db.add(user)
                    await db.commit()
                    await db.refresh(user)
                    
                    logger.info(f"Created new SSO user: {email}")
                    return user
                    
        except Exception as e:
            logger.error(f"Error finding/creating SSO user: {e}")
            return None


# Global instance
sso_service = SSOService()
