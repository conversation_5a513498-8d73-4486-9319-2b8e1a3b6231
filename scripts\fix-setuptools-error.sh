#!/bin/bash

# VietLaw.AI Setuptools Error Fix Script for Linux/macOS
# Fixes the "Cannot import 'setuptools.build_meta'" error

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}VietLaw.AI Setuptools Error Fix${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

log_info "This script will fix the setuptools.build_meta error"
log_info "by recreating the Python virtual environment"
echo ""

# Check if we're in the right directory
if [ ! -d "$BACKEND_DIR" ]; then
    log_error "Backend directory not found: $BACKEND_DIR"
    log_error "Please run this script from the project root"
    exit 1
fi

cd "$BACKEND_DIR"

echo -e "${BLUE}[STEP 1]${NC} Removing corrupted virtual environment..."
if [ -d "venv" ]; then
    log_info "Removing existing venv directory..."
    rm -rf venv
    log_success "Old virtual environment removed"
else
    log_info "No existing virtual environment found"
fi

echo ""
echo -e "${BLUE}[STEP 2]${NC} Creating fresh virtual environment..."
python3 -m venv venv
if [ $? -ne 0 ]; then
    log_error "Failed to create virtual environment"
    log_info "Make sure Python 3.9+ is installed and accessible"
    exit 1
fi
log_success "Virtual environment created"

echo ""
echo -e "${BLUE}[STEP 3]${NC} Activating virtual environment..."
source venv/bin/activate
if [ $? -ne 0 ]; then
    log_error "Failed to activate virtual environment"
    exit 1
fi
log_success "Virtual environment activated"

echo ""
echo -e "${BLUE}[STEP 4]${NC} Installing essential build tools..."
log_info "Installing pip, setuptools, and wheel..."

# Install build tools first with specific versions that work well together
python -m pip install --upgrade pip>=23.0
if [ $? -ne 0 ]; then
    log_error "Failed to upgrade pip"
    exit 1
fi

pip install --upgrade setuptools>=65.0 wheel>=0.38.0
if [ $? -ne 0 ]; then
    log_error "Failed to install setuptools and wheel"
    exit 1
fi

log_success "Build tools installed successfully"

echo ""
echo -e "${BLUE}[STEP 5]${NC} Installing PyTorch (CPU version for compatibility)..."
pip install "torch>=2.2.0,<2.8.0" --index-url https://download.pytorch.org/whl/cpu
if [ $? -ne 0 ]; then
    log_error "Failed to install PyTorch"
    log_info "Trying alternative installation..."
    pip install torch --index-url https://download.pytorch.org/whl/cpu
    if [ $? -ne 0 ]; then
        log_error "PyTorch installation failed completely"
        exit 1
    fi
fi
log_success "PyTorch installed successfully"

echo ""
echo -e "${BLUE}[STEP 6]${NC} Installing other dependencies..."
log_info "This may take a few minutes..."

# Install dependencies one by one to catch specific errors
log_info "Installing core web framework dependencies..."
pip install fastapi uvicorn pydantic sqlalchemy alembic
if [ $? -ne 0 ]; then
    log_error "Failed to install core web framework dependencies"
    exit 1
fi

log_info "Installing database dependencies..."
pip install psycopg2-binary asyncpg pgvector redis aioredis
if [ $? -ne 0 ]; then
    log_error "Failed to install database dependencies"
    exit 1
fi

log_info "Installing ML dependencies..."
pip install sentence-transformers transformers numpy scikit-learn pandas
if [ $? -ne 0 ]; then
    log_error "Failed to install ML dependencies"
    exit 1
fi

log_info "Installing Vietnamese NLP dependencies..."
pip install underthesea pyvi
if [ $? -ne 0 ]; then
    log_error "Failed to install Vietnamese NLP dependencies"
    exit 1
fi

# Install remaining dependencies
log_info "Installing remaining dependencies..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    log_warning "Some dependencies from requirements.txt failed"
    log_info "But core dependencies are installed"
fi

log_success "Dependencies installed successfully"

echo ""
echo -e "${BLUE}[STEP 7]${NC} Verifying installation..."
python -c "import fastapi, sqlalchemy, torch, sentence_transformers, underthesea; print('All core dependencies verified!')"
if [ $? -ne 0 ]; then
    log_error "Verification failed"
    exit 1
fi

log_success "Installation verified successfully!"

echo ""
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}SETUPTOOLS ERROR FIXED SUCCESSFULLY!${NC}"
echo -e "${GREEN}========================================${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. The virtual environment is now ready"
echo "2. To activate it: cd backend && source venv/bin/activate"
echo "3. To start the backend: uvicorn main:app --reload"
echo "4. To install frontend: cd frontend && npm install --legacy-peer-deps"
echo ""
log_info "If you encounter any issues, run this script again"
echo ""
