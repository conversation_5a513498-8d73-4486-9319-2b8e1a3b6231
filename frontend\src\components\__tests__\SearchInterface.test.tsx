/**
 * VietLaw.AI Search Interface Tests
 * Comprehensive testing for search functionality
 */

import React from 'react'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BrowserRouter } from 'react-router-dom'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'

import SearchInterface from '../Search/SearchInterface'
import { useAuthStore } from '../../store/authStore'
import { useSearchStore } from '../../store/searchStore'

// Mock stores
vi.mock('../../store/authStore')
vi.mock('../../store/searchStore')

// Mock API
const mockSearchAPI = vi.fn()
vi.mock('../../services/api', () => ({
  searchAPI: {
    semanticSearch: mockSearchAPI,
    advancedSearch: mockSearchAPI,
    getSuggestions: vi.fn().mockResolvedValue([
      'Luật Lao động',
      'Bộ luật Dân sự',
      'Nghị định 15/2020'
    ])
  }
}))

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  )
}

describe('SearchInterface', () => {
  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    subscription_plan: 'professional',
    can_access_feature: vi.fn().mockReturnValue(true)
  }

  const mockSearchResults = {
    results: [
      {
        id: '1',
        title: 'Luật Lao động số 45/2019/QH14',
        summary: 'Quy định về quyền và nghĩa vụ của người lao động',
        document_type: 'law',
        issuing_authority: 'Quốc hội',
        issue_date: '2019-11-20',
        relevance_score: 0.95,
        highlights: ['người lao động', 'quyền và nghĩa vụ']
      },
      {
        id: '2',
        title: 'Nghị định 145/2020/NĐ-CP',
        summary: 'Quy định chi tiết thi hành một số điều của Luật Lao động',
        document_type: 'decree',
        issuing_authority: 'Chính phủ',
        issue_date: '2020-12-14',
        relevance_score: 0.87,
        highlights: ['thi hành', 'Luật Lao động']
      }
    ],
    total_results: 2,
    query_time: 0.15,
    suggestions: ['Luật Lao động 2019', 'quyền người lao động']
  }

  beforeEach(() => {
    // Mock auth store
    vi.mocked(useAuthStore).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      isLoading: false
    } as any)

    // Mock search store
    vi.mocked(useSearchStore).mockReturnValue({
      searchHistory: [],
      addToHistory: vi.fn(),
      clearHistory: vi.fn(),
      recentSearches: []
    } as any)

    // Reset API mock
    mockSearchAPI.mockResolvedValue(mockSearchResults)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('renders search interface correctly', () => {
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      expect(screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /tìm kiếm/i })).toBeInTheDocument()
      expect(screen.getByText(/tìm kiếm nâng cao/i)).toBeInTheDocument()
    })

    it('shows search suggestions when typing', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i)
      
      await user.type(searchInput, 'Luật')
      
      await waitFor(() => {
        expect(screen.getByText('Luật Lao động')).toBeInTheDocument()
        expect(screen.getByText('Bộ luật Dân sự')).toBeInTheDocument()
      })
    })
  })

  describe('Search Functionality', () => {
    it('performs basic search correctly', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i)
      const searchButton = screen.getByRole('button', { name: /tìm kiếm/i })

      await user.type(searchInput, 'quyền người lao động')
      await user.click(searchButton)

      await waitFor(() => {
        expect(mockSearchAPI).toHaveBeenCalledWith({
          query: 'quyền người lao động',
          search_type: 'semantic',
          limit: 20
        })
      })
    })

    it('displays search results correctly', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i)
      await user.type(searchInput, 'Luật Lao động')
      
      fireEvent.submit(searchInput.closest('form')!)

      await waitFor(() => {
        expect(screen.getByText('Luật Lao động số 45/2019/QH14')).toBeInTheDocument()
        expect(screen.getByText('Nghị định 145/2020/NĐ-CP')).toBeInTheDocument()
        expect(screen.getByText('2 kết quả')).toBeInTheDocument()
      })
    })

    it('handles search errors gracefully', async () => {
      mockSearchAPI.mockRejectedValue(new Error('Search failed'))
      
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i)
      await user.type(searchInput, 'test query')
      
      fireEvent.submit(searchInput.closest('form')!)

      await waitFor(() => {
        expect(screen.getByText(/có lỗi xảy ra/i)).toBeInTheDocument()
      })
    })
  })

  describe('Advanced Search', () => {
    it('opens advanced search modal', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      const advancedButton = screen.getByText(/tìm kiếm nâng cao/i)
      await user.click(advancedButton)

      await waitFor(() => {
        expect(screen.getByText(/bộ lọc nâng cao/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/loại văn bản/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/cơ quan ban hành/i)).toBeInTheDocument()
      })
    })

    it('applies advanced search filters', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      // Open advanced search
      await user.click(screen.getByText(/tìm kiếm nâng cao/i))

      // Set filters
      const documentTypeSelect = screen.getByLabelText(/loại văn bản/i)
      await user.selectOptions(documentTypeSelect, 'law')

      const dateFromInput = screen.getByLabelText(/từ ngày/i)
      await user.type(dateFromInput, '2019-01-01')

      // Perform search
      const searchButton = screen.getByRole('button', { name: /áp dụng bộ lọc/i })
      await user.click(searchButton)

      await waitFor(() => {
        expect(mockSearchAPI).toHaveBeenCalledWith(
          expect.objectContaining({
            document_types: ['law'],
            date_from: '2019-01-01'
          })
        )
      })
    })
  })

  describe('Search History', () => {
    it('displays recent searches', async () => {
      const mockSearchStore = {
        searchHistory: [
          { query: 'Luật Lao động', timestamp: new Date().toISOString() },
          { query: 'Bộ luật Dân sự', timestamp: new Date().toISOString() }
        ],
        addToHistory: vi.fn(),
        clearHistory: vi.fn(),
        recentSearches: []
      }

      vi.mocked(useSearchStore).mockReturnValue(mockSearchStore as any)
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      // Click on search input to show history
      const searchInput = screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i)
      fireEvent.focus(searchInput)

      await waitFor(() => {
        expect(screen.getByText('Luật Lao động')).toBeInTheDocument()
        expect(screen.getByText('Bộ luật Dân sự')).toBeInTheDocument()
      })
    })

    it('clears search history', async () => {
      const mockClearHistory = vi.fn()
      const mockSearchStore = {
        searchHistory: [
          { query: 'Luật Lao động', timestamp: new Date().toISOString() }
        ],
        addToHistory: vi.fn(),
        clearHistory: mockClearHistory,
        recentSearches: []
      }

      vi.mocked(useSearchStore).mockReturnValue(mockSearchStore as any)
      
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      // Open history and clear
      fireEvent.focus(screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i))
      
      await waitFor(() => {
        const clearButton = screen.getByRole('button', { name: /xóa lịch sử/i })
        return user.click(clearButton)
      })

      expect(mockClearHistory).toHaveBeenCalled()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      expect(screen.getByLabelText(/tìm kiếm văn bản pháp luật/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /tìm kiếm/i })).toBeInTheDocument()
      expect(screen.getByRole('searchbox')).toBeInTheDocument()
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i)
      
      // Focus input
      await user.tab()
      expect(searchInput).toHaveFocus()

      // Tab to search button
      await user.tab()
      expect(screen.getByRole('button', { name: /tìm kiếm/i })).toHaveFocus()

      // Enter key should trigger search
      await user.type(searchInput, 'test query')
      await user.keyboard('{Enter}')

      await waitFor(() => {
        expect(mockSearchAPI).toHaveBeenCalled()
      })
    })
  })

  describe('Responsive Design', () => {
    it('adapts to mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375
      })

      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      // Should show mobile-optimized layout
      const searchContainer = screen.getByRole('search')
      expect(searchContainer).toHaveClass('mobile-search')
    })
  })

  describe('Performance', () => {
    it('debounces search suggestions', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i)
      
      // Type quickly
      await user.type(searchInput, 'Luật', { delay: 50 })
      
      // Should not call API immediately
      expect(mockSearchAPI).not.toHaveBeenCalled()
      
      // Wait for debounce
      await waitFor(() => {
        expect(screen.getByText('Luật Lao động')).toBeInTheDocument()
      }, { timeout: 1000 })
    })

    it('cancels previous requests when new search is made', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i)
      
      // Start first search
      await user.type(searchInput, 'first query')
      fireEvent.submit(searchInput.closest('form')!)
      
      // Start second search immediately
      await user.clear(searchInput)
      await user.type(searchInput, 'second query')
      fireEvent.submit(searchInput.closest('form')!)

      // Should handle concurrent requests properly
      await waitFor(() => {
        expect(mockSearchAPI).toHaveBeenCalledTimes(2)
      })
    })
  })

  describe('Vietnamese Language Support', () => {
    it('handles Vietnamese diacritics correctly', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i)
      
      // Type Vietnamese text with diacritics
      await user.type(searchInput, 'quyền và nghĩa vụ của người lao động')
      fireEvent.submit(searchInput.closest('form')!)

      await waitFor(() => {
        expect(mockSearchAPI).toHaveBeenCalledWith(
          expect.objectContaining({
            query: 'quyền và nghĩa vụ của người lao động'
          })
        )
      })
    })

    it('provides Vietnamese error messages', async () => {
      mockSearchAPI.mockRejectedValue(new Error('Network error'))
      
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <SearchInterface />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText(/tìm kiếm văn bản pháp luật/i)
      await user.type(searchInput, 'test')
      fireEvent.submit(searchInput.closest('form')!)

      await waitFor(() => {
        expect(screen.getByText(/có lỗi xảy ra khi tìm kiếm/i)).toBeInTheDocument()
      })
    })
  })
})
