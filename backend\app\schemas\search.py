"""
VietLaw.AI Search Schemas
Pydantic schemas for search-related API endpoints
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class SearchType(str, Enum):
    """Search type enumeration"""
    semantic = "semantic"
    advanced = "advanced"
    vector = "vector"
    similar = "similar"


class DocumentType(str, Enum):
    """Document type enumeration"""
    law = "law"
    decree = "decree"
    circular = "circular"
    decision = "decision"
    constitution = "constitution"
    treaty = "treaty"
    precedent = "precedent"


class SortOrder(str, Enum):
    """Sort order enumeration"""
    relevance = "relevance"
    date_desc = "date_desc"
    date_asc = "date_asc"
    title_asc = "title_asc"
    title_desc = "title_desc"


class SearchFilters(BaseModel):
    """Search filters schema"""
    document_types: Optional[List[DocumentType]] = Field(None, description="Filter by document types")
    issuing_authorities: Optional[List[str]] = Field(None, description="Filter by issuing authorities")
    date_from: Optional[datetime] = Field(None, description="Filter documents from this date")
    date_to: Optional[datetime] = Field(None, description="Filter documents to this date")
    keywords: Optional[List[str]] = Field(None, description="Additional keywords to filter")
    tags: Optional[List[str]] = Field(None, description="Filter by document tags")
    status: Optional[str] = Field("active", description="Document status filter")
    language: Optional[str] = Field("vi", description="Document language")
    
    class Config:
        schema_extra = {
            "example": {
                "document_types": ["law", "decree"],
                "issuing_authorities": ["Quốc hội", "Chính phủ"],
                "date_from": "2020-01-01T00:00:00Z",
                "date_to": "2024-12-31T23:59:59Z",
                "keywords": ["lao động", "bảo hiểm"],
                "tags": ["employment", "social_security"],
                "status": "active",
                "language": "vi"
            }
        }


class SearchRequest(BaseModel):
    """Search request schema"""
    query: str = Field(..., min_length=1, max_length=1000, description="Search query text")
    search_type: SearchType = Field(SearchType.semantic, description="Type of search to perform")
    filters: Optional[SearchFilters] = Field(None, description="Search filters")
    limit: int = Field(10, ge=1, le=100, description="Maximum number of results")
    offset: int = Field(0, ge=0, description="Number of results to skip")
    sort_by: SortOrder = Field(SortOrder.relevance, description="Sort order for results")
    include_content: bool = Field(False, description="Include full document content in results")
    include_highlights: bool = Field(True, description="Include search term highlights")
    similarity_threshold: Optional[float] = Field(0.7, ge=0.0, le=1.0, description="Similarity threshold for vector search")
    
    @validator('query')
    def validate_query(cls, v):
        if not v.strip():
            raise ValueError('Query cannot be empty')
        return v.strip()
    
    class Config:
        schema_extra = {
            "example": {
                "query": "quyền lao động của người lao động",
                "search_type": "semantic",
                "filters": {
                    "document_types": ["law"],
                    "date_from": "2020-01-01T00:00:00Z"
                },
                "limit": 20,
                "offset": 0,
                "sort_by": "relevance",
                "include_content": False,
                "include_highlights": True,
                "similarity_threshold": 0.75
            }
        }


class SearchHighlight(BaseModel):
    """Search result highlight schema"""
    field: str = Field(..., description="Field name where highlight was found")
    fragments: List[str] = Field(..., description="Highlighted text fragments")
    
    class Config:
        schema_extra = {
            "example": {
                "field": "content",
                "fragments": [
                    "Người lao động có <mark>quyền</mark> được bảo vệ",
                    "<mark>Quyền lao động</mark> được quy định trong luật"
                ]
            }
        }


class SearchResult(BaseModel):
    """Individual search result schema"""
    document_id: str = Field(..., description="Document unique identifier")
    chunk_id: Optional[str] = Field(None, description="Chunk identifier for vector search")
    title: str = Field(..., description="Document title")
    document_type: DocumentType = Field(..., description="Type of legal document")
    document_number: Optional[str] = Field(None, description="Official document number")
    issuing_authority: Optional[str] = Field(None, description="Authority that issued the document")
    issue_date: Optional[datetime] = Field(None, description="Date when document was issued")
    effective_date: Optional[datetime] = Field(None, description="Date when document becomes effective")
    status: str = Field(..., description="Document status")
    summary: Optional[str] = Field(None, description="Document summary")
    content: Optional[str] = Field(None, description="Document content (if requested)")
    content_snippet: Optional[str] = Field(None, description="Relevant content snippet")
    similarity_score: float = Field(..., ge=0.0, le=1.0, description="Similarity score to query")
    relevance_score: float = Field(..., ge=0.0, le=1.0, description="Overall relevance score")
    highlights: Optional[List[SearchHighlight]] = Field(None, description="Search term highlights")
    keywords: Optional[List[str]] = Field(None, description="Document keywords")
    tags: Optional[List[str]] = Field(None, description="Document tags")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    search_time_ms: Optional[int] = Field(None, description="Time taken for this result")
    
    class Config:
        schema_extra = {
            "example": {
                "document_id": "123e4567-e89b-12d3-a456-426614174000",
                "chunk_id": "456e7890-e89b-12d3-a456-426614174001",
                "title": "Bộ luật Lao động năm 2019",
                "document_type": "law",
                "document_number": "45/2019/QH14",
                "issuing_authority": "Quốc hội",
                "issue_date": "2019-11-20T00:00:00Z",
                "effective_date": "2021-01-01T00:00:00Z",
                "status": "active",
                "summary": "Quy định về quyền và nghĩa vụ của người lao động và người sử dụng lao động",
                "content_snippet": "Người lao động có quyền được bảo vệ an toàn, sức khỏe trong lao động...",
                "similarity_score": 0.85,
                "relevance_score": 0.92,
                "highlights": [
                    {
                        "field": "content",
                        "fragments": ["<mark>quyền lao động</mark> của người lao động"]
                    }
                ],
                "keywords": ["lao động", "quyền", "nghĩa vụ"],
                "tags": ["employment", "labor_rights"],
                "search_time_ms": 150
            }
        }


class SearchResponse(BaseModel):
    """Search response schema"""
    query: str = Field(..., description="Original search query")
    search_type: SearchType = Field(..., description="Type of search performed")
    results: List[SearchResult] = Field(..., description="Search results")
    total_count: int = Field(..., ge=0, description="Total number of matching documents")
    returned_count: int = Field(..., ge=0, description="Number of results returned")
    offset: int = Field(..., ge=0, description="Offset used in search")
    limit: int = Field(..., ge=1, description="Limit used in search")
    search_time_ms: int = Field(..., ge=0, description="Total search time in milliseconds")
    filters_applied: Optional[SearchFilters] = Field(None, description="Filters that were applied")
    suggestions: Optional[List[str]] = Field(None, description="Query suggestions for better results")
    facets: Optional[Dict[str, List[Dict[str, Any]]]] = Field(None, description="Search facets for filtering")
    
    @validator('returned_count')
    def validate_returned_count(cls, v, values):
        if 'results' in values and len(values['results']) != v:
            raise ValueError('returned_count must match the number of results')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "query": "quyền lao động của người lao động",
                "search_type": "semantic",
                "results": [],  # Would contain SearchResult objects
                "total_count": 150,
                "returned_count": 10,
                "offset": 0,
                "limit": 10,
                "search_time_ms": 250,
                "filters_applied": {
                    "document_types": ["law"],
                    "status": "active"
                },
                "suggestions": [
                    "quyền và nghĩa vụ của người lao động",
                    "bảo vệ quyền lợi người lao động"
                ],
                "facets": {
                    "document_types": [
                        {"value": "law", "count": 45},
                        {"value": "decree", "count": 32}
                    ],
                    "issuing_authorities": [
                        {"value": "Quốc hội", "count": 25},
                        {"value": "Chính phủ", "count": 52}
                    ]
                }
            }
        }


class SearchSuggestion(BaseModel):
    """Search suggestion schema"""
    text: str = Field(..., description="Suggested search text")
    type: str = Field(..., description="Type of suggestion")
    score: float = Field(..., ge=0.0, le=1.0, description="Suggestion relevance score")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional suggestion metadata")
    
    class Config:
        schema_extra = {
            "example": {
                "text": "quyền và nghĩa vụ của người lao động",
                "type": "query_completion",
                "score": 0.95,
                "metadata": {
                    "frequency": 150,
                    "category": "labor_law"
                }
            }
        }


class SearchAnalyticsRequest(BaseModel):
    """Search analytics request schema"""
    start_date: datetime = Field(..., description="Start date for analytics")
    end_date: datetime = Field(..., description="End date for analytics")
    group_by: Optional[str] = Field("day", description="Grouping period (day, week, month)")
    metrics: Optional[List[str]] = Field(None, description="Specific metrics to include")
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "start_date": "2024-01-01T00:00:00Z",
                "end_date": "2024-01-31T23:59:59Z",
                "group_by": "day",
                "metrics": ["total_searches", "unique_users", "avg_search_time"]
            }
        }


class SearchAnalyticsResponse(BaseModel):
    """Search analytics response schema"""
    period: str = Field(..., description="Analytics period")
    total_searches: int = Field(..., description="Total number of searches")
    unique_users: int = Field(..., description="Number of unique users")
    avg_search_time_ms: float = Field(..., description="Average search time")
    top_queries: List[Dict[str, Any]] = Field(..., description="Most popular queries")
    search_types: Dict[str, int] = Field(..., description="Breakdown by search type")
    success_rate: float = Field(..., description="Percentage of successful searches")
    
    class Config:
        schema_extra = {
            "example": {
                "period": "2024-01-01 to 2024-01-31",
                "total_searches": 1250,
                "unique_users": 85,
                "avg_search_time_ms": 245.5,
                "top_queries": [
                    {"query": "quyền lao động", "count": 45},
                    {"query": "bảo hiểm xã hội", "count": 32}
                ],
                "search_types": {
                    "semantic": 800,
                    "advanced": 350,
                    "vector": 100
                },
                "success_rate": 0.92
            }
        }
