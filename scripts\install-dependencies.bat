@echo off
REM VietLaw.AI Dependency Installation Script for Windows
REM Handles installation of all project dependencies with error handling

setlocal enabledelayedexpansion

REM Configuration
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..

REM Colors (using echo with special characters)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM Functions
:log_info
echo %BLUE%[INFO]%NC% %~1
goto :eof

:log_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:log_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

:check_prerequisites
call :log_info "Checking prerequisites..."

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    call :log_error "Python is required but not installed"
    exit /b 1
)

REM Check Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
call :log_info "Python version: %PYTHON_VERSION%"

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    call :log_error "Node.js is required but not installed"
    exit /b 1
)

REM Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    call :log_error "npm is required but not installed"
    exit /b 1
)

call :log_success "Prerequisites check passed"
goto :eof

:install_backend_dependencies
call :log_info "Installing backend dependencies..."

cd /d "%PROJECT_ROOT%\backend"

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    call :log_info "Creating Python virtual environment..."
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Install essential build tools first
call :log_info "Installing essential build tools..."
python -m pip install --upgrade pip>=23.0
pip install --upgrade setuptools>=65.0 wheel>=0.38.0

REM Install PyTorch first with CPU-only version for compatibility
call :log_info "Installing PyTorch..."
pip install "torch>=2.2.0,<2.8.0" --index-url https://download.pytorch.org/whl/cpu

REM Install other dependencies
call :log_info "Installing Python packages..."
pip install -r requirements.txt

if errorlevel 1 (
    call :log_error "Failed to install backend dependencies"
    exit /b 1
)

call :log_success "Backend dependencies installed successfully"
goto :eof

:install_frontend_dependencies
call :log_info "Installing frontend dependencies..."

cd /d "%PROJECT_ROOT%\frontend"

REM Clear npm cache
call :log_info "Clearing npm cache..."
npm cache clean --force

REM Install dependencies
call :log_info "Installing Node.js packages..."
npm install

if errorlevel 1 (
    call :log_warning "npm install failed, trying with legacy peer deps..."
    npm install --legacy-peer-deps
)

if errorlevel 1 (
    call :log_error "Failed to install frontend dependencies"
    exit /b 1
)

call :log_success "Frontend dependencies installed successfully"
goto :eof

:install_integration_dependencies
call :log_info "Installing integration dependencies..."

REM Microsoft Office integration
cd /d "%PROJECT_ROOT%\integrations\office"
if exist "package.json" (
    npm install
)

REM Google Workspace integration
cd /d "%PROJECT_ROOT%\integrations\google-workspace"
if exist "package.json" (
    npm install
)

call :log_success "Integration dependencies installed successfully"
goto :eof

:verify_installation
call :log_info "Verifying installation..."

REM Test backend imports
cd /d "%PROJECT_ROOT%\backend"
call venv\Scripts\activate.bat

python -c "import fastapi; import sqlalchemy; import torch; import sentence_transformers; import underthesea; print('Backend dependencies verified successfully')"

if errorlevel 1 (
    call :log_error "Backend dependency verification failed"
    exit /b 1
)

REM Test frontend build
cd /d "%PROJECT_ROOT%\frontend"
npm run type-check

if errorlevel 1 (
    call :log_error "Frontend type checking failed"
    exit /b 1
)

call :log_success "Installation verification completed"
goto :eof

:fix_common_issues
call :log_info "Applying fixes for common issues..."

REM Fix torch installation issues
cd /d "%PROJECT_ROOT%\backend"
call venv\Scripts\activate.bat

call :log_info "Reinstalling PyTorch with CPU-only version..."
pip uninstall torch torchvision torchaudio -y
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

REM Fix npm issues
cd /d "%PROJECT_ROOT%\frontend"

call :log_info "Cleaning npm cache and node_modules..."
if exist "node_modules" rmdir /s /q node_modules
if exist "package-lock.json" del package-lock.json

call :log_info "Reinstalling npm packages with legacy peer deps..."
npm install --legacy-peer-deps

call :log_success "Common issues fixed"
goto :eof

:show_usage
echo VietLaw.AI Dependency Installation Script for Windows
echo.
echo Usage: %~nx0 [OPTIONS]
echo.
echo Options:
echo     --backend-only      Install only backend dependencies
echo     --frontend-only     Install only frontend dependencies
echo     --fix-issues        Fix common dependency issues
echo     --verify-only       Only verify existing installation
echo     -h, --help          Show this help message
echo.
echo Examples:
echo     %~nx0                  # Install all dependencies
echo     %~nx0 --backend-only   # Install only backend
echo     %~nx0 --fix-issues     # Fix common issues
echo.
goto :eof

:main
call :log_info "Starting VietLaw.AI dependency installation..."

call :check_prerequisites
if errorlevel 1 exit /b 1

if "%~1"=="--backend-only" (
    call :install_backend_dependencies
) else if "%~1"=="--frontend-only" (
    call :install_frontend_dependencies
) else if "%~1"=="--fix-issues" (
    call :fix_common_issues
) else if "%~1"=="--verify-only" (
    call :verify_installation
) else if "%~1"=="-h" (
    call :show_usage
    exit /b 0
) else if "%~1"=="--help" (
    call :show_usage
    exit /b 0
) else (
    call :install_backend_dependencies
    if errorlevel 1 exit /b 1
    
    call :install_frontend_dependencies
    if errorlevel 1 exit /b 1
    
    call :install_integration_dependencies
    if errorlevel 1 exit /b 1
    
    call :verify_installation
    if errorlevel 1 exit /b 1
)

call :log_success "Dependency installation completed successfully!"

echo.
call :log_info "Next steps:"
echo 1. Copy .env.example to .env and configure your environment variables
echo 2. Start the database: docker-compose up -d postgres redis
echo 3. Run database migrations: cd backend ^&^& python migrate.py
echo 4. Start the backend: cd backend ^&^& venv\Scripts\activate ^&^& uvicorn main:app --reload
echo 5. Start the frontend: cd frontend ^&^& npm run dev

goto :eof

REM Main execution
call :main %*
