"""
VietLaw.AI Vector Service
Vector operations and similarity search using pgvector
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple
import asyncio
from sqlalchemy import text, select, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db_session_context
from app.models.document import DocumentChunk, LegalDocument
from app.services.vietnamese_nlp import vietnamese_nlp

logger = logging.getLogger(__name__)


class VectorService:
    """Vector operations service for semantic search"""
    
    def __init__(self, db: Optional[AsyncSession] = None):
        self.db = db
        self.embedding_dimension = 768
        self.similarity_threshold = 0.7
    
    async def store_document_embeddings(
        self,
        document_id: str,
        chunks: List[Dict],
        batch_size: int = 100
    ) -> bool:
        """
        Store document chunk embeddings in vector database
        
        Args:
            document_id: Document identifier
            chunks: List of text chunks with embeddings
            batch_size: Batch size for database operations
        
        Returns:
            Success status
        """
        try:
            async with get_db_session_context() as session:
                # Process chunks in batches
                for i in range(0, len(chunks), batch_size):
                    batch = chunks[i:i + batch_size]
                    
                    # Create DocumentChunk objects
                    chunk_objects = []
                    for chunk_data in batch:
                        chunk_obj = DocumentChunk(
                            document_id=document_id,
                            chunk_index=chunk_data["chunk_index"],
                            content=chunk_data["content"],
                            content_vietnamese=chunk_data.get("content_vietnamese"),
                            embedding=chunk_data["embedding"],
                            token_count=chunk_data.get("token_count", 0),
                            chunk_type=chunk_data.get("chunk_type", "paragraph"),
                            start_position=chunk_data.get("start_position", 0),
                            end_position=chunk_data.get("end_position", 0),
                            metadata=chunk_data.get("metadata", {})
                        )
                        chunk_objects.append(chunk_obj)
                    
                    # Bulk insert
                    session.add_all(chunk_objects)
                    await session.flush()
                
                await session.commit()
                logger.info(f"Stored {len(chunks)} embeddings for document {document_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error storing embeddings for document {document_id}: {e}")
            return False
    
    async def similarity_search(
        self,
        query_embedding: List[float],
        limit: int = 10,
        threshold: float = None,
        document_types: List[str] = None,
        date_range: Tuple[str, str] = None
    ) -> List[Dict]:
        """
        Perform vector similarity search
        
        Args:
            query_embedding: Query vector embedding
            limit: Maximum number of results
            threshold: Similarity threshold
            document_types: Filter by document types
            date_range: Filter by date range (start, end)
        
        Returns:
            List of similar documents with scores
        """
        try:
            threshold = threshold or self.similarity_threshold
            
            async with get_db_session_context() as session:
                # Build query
                query = """
                SELECT 
                    dc.id,
                    dc.document_id,
                    dc.content,
                    dc.chunk_index,
                    dc.metadata,
                    ld.title,
                    ld.document_type,
                    ld.issuing_authority,
                    ld.issue_date,
                    (dc.embedding <=> :query_embedding) as distance,
                    (1 - (dc.embedding <=> :query_embedding)) as similarity
                FROM document_chunks dc
                JOIN legal_documents ld ON dc.document_id = ld.id
                WHERE (dc.embedding <=> :query_embedding) < :threshold
                """
                
                params = {
                    "query_embedding": query_embedding,
                    "threshold": 1 - threshold
                }
                
                # Add filters
                if document_types:
                    query += " AND ld.document_type = ANY(:document_types)"
                    params["document_types"] = document_types
                
                if date_range:
                    query += " AND ld.issue_date BETWEEN :start_date AND :end_date"
                    params["start_date"] = date_range[0]
                    params["end_date"] = date_range[1]
                
                query += " ORDER BY distance ASC LIMIT :limit"
                params["limit"] = limit
                
                # Execute query
                result = await session.execute(text(query), params)
                rows = result.fetchall()
                
                # Format results
                results = []
                for row in rows:
                    results.append({
                        "chunk_id": str(row.id),
                        "document_id": str(row.document_id),
                        "content": row.content,
                        "chunk_index": row.chunk_index,
                        "metadata": row.metadata,
                        "document_title": row.title,
                        "document_type": row.document_type,
                        "issuing_authority": row.issuing_authority,
                        "issue_date": row.issue_date.isoformat() if row.issue_date else None,
                        "similarity_score": float(row.similarity),
                        "distance": float(row.distance)
                    })
                
                return results
                
        except Exception as e:
            logger.error(f"Error in similarity search: {e}")
            return []
    
    async def semantic_search(
        self,
        query_text: str,
        limit: int = 10,
        threshold: float = None,
        filters: Dict = None
    ) -> List[Dict]:
        """
        Perform semantic search using text query
        
        Args:
            query_text: Search query text
            limit: Maximum number of results
            threshold: Similarity threshold
            filters: Additional search filters
        
        Returns:
            List of search results
        """
        try:
            # Generate embedding for query
            query_embedding = await vietnamese_nlp.generate_embedding(query_text)
            
            if query_embedding is None or len(query_embedding) == 0:
                logger.error("Failed to generate query embedding")
                return []
            
            # Extract filters
            document_types = filters.get("document_types") if filters else None
            date_range = filters.get("date_range") if filters else None
            
            # Perform similarity search
            results = await self.similarity_search(
                query_embedding=query_embedding.tolist() if hasattr(query_embedding, 'tolist') else query_embedding,
                limit=limit,
                threshold=threshold,
                document_types=document_types,
                date_range=date_range
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error in semantic search: {e}")
            return []
    
    async def find_similar_documents(
        self,
        document_id: str,
        limit: int = 10,
        threshold: float = None
    ) -> List[Dict]:
        """
        Find documents similar to a given document
        
        Args:
            document_id: Reference document ID
            limit: Maximum number of results
            threshold: Similarity threshold
        
        Returns:
            List of similar documents
        """
        try:
            async with get_db_session_context() as session:
                # Get document chunks
                result = await session.execute(
                    select(DocumentChunk.embedding)
                    .where(DocumentChunk.document_id == document_id)
                    .limit(5)  # Use first 5 chunks for similarity
                )
                embeddings = [row[0] for row in result.fetchall()]
                
                if not embeddings:
                    logger.warning(f"No embeddings found for document {document_id}")
                    return []
                
                # Calculate average embedding
                avg_embedding = np.mean(embeddings, axis=0)
                
                # Find similar documents
                results = await self.similarity_search(
                    query_embedding=avg_embedding.tolist(),
                    limit=limit + 10,  # Get extra to filter out self
                    threshold=threshold
                )
                
                # Filter out the same document
                filtered_results = [
                    result for result in results 
                    if result["document_id"] != document_id
                ]
                
                return filtered_results[:limit]
                
        except Exception as e:
            logger.error(f"Error finding similar documents: {e}")
            return []
    
    async def get_document_embedding(self, document_id: str) -> Optional[np.ndarray]:
        """
        Get average embedding for a document
        
        Args:
            document_id: Document identifier
        
        Returns:
            Average embedding vector
        """
        try:
            async with get_db_session_context() as session:
                result = await session.execute(
                    select(DocumentChunk.embedding)
                    .where(DocumentChunk.document_id == document_id)
                )
                embeddings = [row[0] for row in result.fetchall()]
                
                if not embeddings:
                    return None
                
                # Calculate average embedding
                avg_embedding = np.mean(embeddings, axis=0)
                return avg_embedding
                
        except Exception as e:
            logger.error(f"Error getting document embedding: {e}")
            return None
    
    async def calculate_document_similarity(
        self,
        document1_id: str,
        document2_id: str
    ) -> float:
        """
        Calculate similarity between two documents
        
        Args:
            document1_id: First document ID
            document2_id: Second document ID
        
        Returns:
            Similarity score (0-1)
        """
        try:
            # Get embeddings for both documents
            embedding1 = await self.get_document_embedding(document1_id)
            embedding2 = await self.get_document_embedding(document2_id)
            
            if embedding1 is None or embedding2 is None:
                return 0.0
            
            # Calculate cosine similarity
            similarity = np.dot(embedding1, embedding2) / (
                np.linalg.norm(embedding1) * np.linalg.norm(embedding2)
            )
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Error calculating document similarity: {e}")
            return 0.0
    
    async def update_document_embeddings(
        self,
        document_id: str,
        new_chunks: List[Dict]
    ) -> bool:
        """
        Update embeddings for a document
        
        Args:
            document_id: Document identifier
            new_chunks: New chunk data with embeddings
        
        Returns:
            Success status
        """
        try:
            async with get_db_session_context() as session:
                # Delete existing chunks
                await session.execute(
                    text("DELETE FROM document_chunks WHERE document_id = :document_id"),
                    {"document_id": document_id}
                )
                
                # Store new embeddings
                success = await self.store_document_embeddings(
                    document_id=document_id,
                    chunks=new_chunks
                )
                
                return success
                
        except Exception as e:
            logger.error(f"Error updating document embeddings: {e}")
            return False
    
    async def delete_document_embeddings(self, document_id: str) -> bool:
        """
        Delete all embeddings for a document
        
        Args:
            document_id: Document identifier
        
        Returns:
            Success status
        """
        try:
            async with get_db_session_context() as session:
                await session.execute(
                    text("DELETE FROM document_chunks WHERE document_id = :document_id"),
                    {"document_id": document_id}
                )
                await session.commit()
                
                logger.info(f"Deleted embeddings for document {document_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error deleting document embeddings: {e}")
            return False
    
    async def get_vector_statistics(self) -> Dict:
        """
        Get vector database statistics
        
        Returns:
            Statistics dictionary
        """
        try:
            async with get_db_session_context() as session:
                # Get basic counts
                result = await session.execute(
                    text("""
                    SELECT 
                        COUNT(*) as total_chunks,
                        COUNT(DISTINCT document_id) as total_documents,
                        AVG(token_count) as avg_tokens_per_chunk,
                        MIN(token_count) as min_tokens,
                        MAX(token_count) as max_tokens
                    FROM document_chunks
                    """)
                )
                stats = result.fetchone()
                
                return {
                    "total_chunks": stats.total_chunks,
                    "total_documents": stats.total_documents,
                    "avg_tokens_per_chunk": float(stats.avg_tokens_per_chunk) if stats.avg_tokens_per_chunk else 0,
                    "min_tokens": stats.min_tokens,
                    "max_tokens": stats.max_tokens,
                    "embedding_dimension": self.embedding_dimension,
                }
                
        except Exception as e:
            logger.error(f"Error getting vector statistics: {e}")
            return {}


# Global instance
vector_service = VectorService()
