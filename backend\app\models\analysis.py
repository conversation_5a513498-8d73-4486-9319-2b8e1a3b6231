"""
VietLaw.AI Analysis Models
SQLAlchemy models for risk analysis, comparisons, and user queries
"""

import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, DateTime, Float, Integer, String, Text, JSON, ForeignKey, Boolean, Enum
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class UserQuery(Base):
    """User query model for search history and analytics"""
    
    __tablename__ = "user_queries"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    query_text = Column(Text, nullable=False)
    query_type = Column(
        Enum("semantic", "advanced", "vector", "similar", name="query_type"),
        default="semantic",
        nullable=False,
        index=True
    )
    
    # Search parameters
    filters_applied = Column(JSON, default=dict, nullable=False)
    results_count = Column(Integer, default=0, nullable=False)
    search_time_ms = Column(Integer, nullable=True)
    
    # Query metadata
    query_embedding = Column(Text, nullable=True)  # Stored as JSON string
    query_keywords = Column(ARRAY(String), nullable=True)
    query_language = Column(String(10), default="vi", nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, index=True)
    
    # Additional metadata
    metadata = Column(JSON, default=dict, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="queries")
    
    def __repr__(self):
        return f"<UserQuery(id={self.id}, user_id={self.user_id}, query_text={self.query_text[:50]}...)>"
    
    def to_dict(self) -> dict:
        """Convert query to dictionary"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "query_text": self.query_text,
            "query_type": self.query_type,
            "filters_applied": self.filters_applied,
            "results_count": self.results_count,
            "search_time_ms": self.search_time_ms,
            "query_keywords": self.query_keywords,
            "query_language": self.query_language,
            "created_at": self.created_at.isoformat(),
            "metadata": self.metadata,
        }


class RiskAnalysis(Base):
    """Risk analysis model for legal document analysis"""
    
    __tablename__ = "risk_analyses"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    document_id = Column(UUID(as_uuid=True), ForeignKey("legal_documents.id", ondelete="CASCADE"), nullable=True, index=True)
    
    # Analysis input
    analysis_text = Column(Text, nullable=False)
    analysis_type = Column(
        Enum("compliance", "contract", "regulatory", "general", name="analysis_type"),
        default="general",
        nullable=False,
        index=True
    )
    
    # Risk assessment results
    overall_risk_score = Column(Float, nullable=False, index=True)
    risk_level = Column(
        Enum("low", "medium", "high", "critical", name="risk_level"),
        nullable=False,
        index=True
    )
    confidence_score = Column(Float, nullable=False)
    
    # Detailed analysis
    risk_factors = Column(JSON, default=list, nullable=False)
    recommendations = Column(JSON, default=list, nullable=False)
    legal_issues = Column(JSON, default=list, nullable=False)
    compliance_gaps = Column(JSON, default=list, nullable=False)
    
    # Analysis metadata
    analysis_duration_ms = Column(Integer, nullable=True)
    model_version = Column(String(50), nullable=True)
    analysis_parameters = Column(JSON, default=dict, nullable=False)
    
    # Status
    status = Column(
        Enum("pending", "processing", "completed", "failed", name="analysis_status"),
        default="pending",
        nullable=False,
        index=True
    )
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, index=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Additional metadata
    metadata = Column(JSON, default=dict, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="risk_analyses")
    document = relationship("LegalDocument", back_populates="risk_analyses")
    
    def __repr__(self):
        return f"<RiskAnalysis(id={self.id}, user_id={self.user_id}, risk_level={self.risk_level})>"
    
    @property
    def is_completed(self) -> bool:
        """Check if analysis is completed"""
        return self.status == "completed"
    
    @property
    def is_high_risk(self) -> bool:
        """Check if analysis indicates high risk"""
        return self.risk_level in ["high", "critical"]
    
    def to_dict(self, include_details: bool = True) -> dict:
        """Convert analysis to dictionary"""
        data = {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "document_id": str(self.document_id) if self.document_id else None,
            "analysis_type": self.analysis_type,
            "overall_risk_score": self.overall_risk_score,
            "risk_level": self.risk_level,
            "confidence_score": self.confidence_score,
            "status": self.status,
            "analysis_duration_ms": self.analysis_duration_ms,
            "model_version": self.model_version,
            "created_at": self.created_at.isoformat(),
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "metadata": self.metadata,
            "is_completed": self.is_completed,
            "is_high_risk": self.is_high_risk,
        }
        
        if include_details:
            data.update({
                "analysis_text": self.analysis_text,
                "risk_factors": self.risk_factors,
                "recommendations": self.recommendations,
                "legal_issues": self.legal_issues,
                "compliance_gaps": self.compliance_gaps,
                "analysis_parameters": self.analysis_parameters,
            })
        
        return data


class DocumentComparison(Base):
    """Document comparison model"""
    
    __tablename__ = "document_comparisons"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # Documents being compared
    document1_id = Column(UUID(as_uuid=True), ForeignKey("legal_documents.id", ondelete="CASCADE"), nullable=True)
    document2_id = Column(UUID(as_uuid=True), ForeignKey("legal_documents.id", ondelete="CASCADE"), nullable=True)
    document1_text = Column(Text, nullable=True)
    document2_text = Column(Text, nullable=True)
    
    # Comparison results
    similarity_score = Column(Float, nullable=False, index=True)
    comparison_type = Column(
        Enum("semantic", "structural", "legal", "full", name="comparison_type"),
        default="semantic",
        nullable=False,
        index=True
    )
    
    # Detailed comparison
    similarities = Column(JSON, default=list, nullable=False)
    differences = Column(JSON, default=list, nullable=False)
    key_changes = Column(JSON, default=list, nullable=False)
    impact_analysis = Column(JSON, default=dict, nullable=False)
    
    # Comparison metadata
    comparison_duration_ms = Column(Integer, nullable=True)
    model_version = Column(String(50), nullable=True)
    comparison_parameters = Column(JSON, default=dict, nullable=False)
    
    # Status
    status = Column(
        Enum("pending", "processing", "completed", "failed", name="comparison_status"),
        default="pending",
        nullable=False,
        index=True
    )
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, index=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Additional metadata
    metadata = Column(JSON, default=dict, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="document_comparisons")
    
    def __repr__(self):
        return f"<DocumentComparison(id={self.id}, user_id={self.user_id}, similarity_score={self.similarity_score})>"
    
    @property
    def is_completed(self) -> bool:
        """Check if comparison is completed"""
        return self.status == "completed"
    
    @property
    def is_highly_similar(self) -> bool:
        """Check if documents are highly similar"""
        return self.similarity_score >= 0.8
    
    def to_dict(self, include_content: bool = False) -> dict:
        """Convert comparison to dictionary"""
        data = {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "document1_id": str(self.document1_id) if self.document1_id else None,
            "document2_id": str(self.document2_id) if self.document2_id else None,
            "similarity_score": self.similarity_score,
            "comparison_type": self.comparison_type,
            "similarities": self.similarities,
            "differences": self.differences,
            "key_changes": self.key_changes,
            "impact_analysis": self.impact_analysis,
            "status": self.status,
            "comparison_duration_ms": self.comparison_duration_ms,
            "model_version": self.model_version,
            "comparison_parameters": self.comparison_parameters,
            "created_at": self.created_at.isoformat(),
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "metadata": self.metadata,
            "is_completed": self.is_completed,
            "is_highly_similar": self.is_highly_similar,
        }
        
        if include_content:
            data.update({
                "document1_text": self.document1_text,
                "document2_text": self.document2_text,
            })
        
        return data


class SearchAnalytics(Base):
    """Search analytics model for tracking search patterns"""
    
    __tablename__ = "search_analytics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # Search metrics
    total_searches = Column(Integer, default=0, nullable=False)
    unique_users = Column(Integer, default=0, nullable=False)
    avg_search_time_ms = Column(Float, nullable=True)
    avg_results_per_search = Column(Float, nullable=True)
    
    # Popular queries
    top_queries = Column(JSON, default=list, nullable=False)
    top_keywords = Column(JSON, default=list, nullable=False)
    
    # Search types
    semantic_searches = Column(Integer, default=0, nullable=False)
    advanced_searches = Column(Integer, default=0, nullable=False)
    vector_searches = Column(Integer, default=0, nullable=False)
    
    # User segments
    basic_users = Column(Integer, default=0, nullable=False)
    professional_users = Column(Integer, default=0, nullable=False)
    enterprise_users = Column(Integer, default=0, nullable=False)
    
    # Additional metrics
    failed_searches = Column(Integer, default=0, nullable=False)
    zero_result_searches = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Additional metadata
    metadata = Column(JSON, default=dict, nullable=False)
    
    def __repr__(self):
        return f"<SearchAnalytics(id={self.id}, date={self.date}, total_searches={self.total_searches})>"
    
    def to_dict(self) -> dict:
        """Convert analytics to dictionary"""
        return {
            "id": str(self.id),
            "date": self.date.isoformat(),
            "total_searches": self.total_searches,
            "unique_users": self.unique_users,
            "avg_search_time_ms": self.avg_search_time_ms,
            "avg_results_per_search": self.avg_results_per_search,
            "top_queries": self.top_queries,
            "top_keywords": self.top_keywords,
            "semantic_searches": self.semantic_searches,
            "advanced_searches": self.advanced_searches,
            "vector_searches": self.vector_searches,
            "basic_users": self.basic_users,
            "professional_users": self.professional_users,
            "enterprise_users": self.enterprise_users,
            "failed_searches": self.failed_searches,
            "zero_result_searches": self.zero_result_searches,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "metadata": self.metadata,
        }
