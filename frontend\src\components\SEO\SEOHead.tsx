/**
 * VietLaw.AI SEO Head Component
 * Comprehensive SEO optimization for Vietnamese legal content
 */

import React from 'react'
import { Helmet } from 'react-helmet-async'

interface SEOHeadProps {
  title?: string
  description?: string
  keywords?: string[]
  canonicalUrl?: string
  ogImage?: string
  ogType?: string
  structuredData?: object
  noIndex?: boolean
  alternateLanguages?: { lang: string; url: string }[]
  breadcrumbs?: { name: string; url: string }[]
  legalDocument?: {
    title: string
    documentType: string
    issuingAuthority: string
    issueDate: string
    documentNumber: string
    effectiveDate?: string
    status: string
  }
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'VietLaw.AI - Trợ lý AI Pháp luật Việt Nam',
  description = '<PERSON><PERSON> thống AI tiên tiến cho tìm kiếm, phân tích và tư vấn pháp luật Việt Nam. Công nghệ RAG hiện đại giúp doanh nghiệp và luật sư tra cứu văn bản pháp luật chính xác và nhanh chóng.',
  keywords = [
    'pháp luật việt nam',
    'AI pháp luật',
    'tìm kiếm văn bản pháp luật',
    'phân tích rủi ro pháp lý',
    'tư vấn pháp luật',
    'RAG legal AI',
    'vietnamese law',
    'legal compliance',
    'luật sư AI',
    'tra cứu luật'
  ],
  canonicalUrl,
  ogImage = '/assets/og-image.jpg',
  ogType = 'website',
  structuredData,
  noIndex = false,
  alternateLanguages = [],
  breadcrumbs = [],
  legalDocument
}) => {
  const siteUrl = process.env.REACT_APP_SITE_URL || 'https://vietlaw.ai'
  const fullTitle = title.includes('VietLaw.AI') ? title : `${title} | VietLaw.AI`
  const fullCanonicalUrl = canonicalUrl || window.location.href
  const fullOgImage = ogImage.startsWith('http') ? ogImage : `${siteUrl}${ogImage}`

  // Generate structured data for legal documents
  const generateLegalDocumentStructuredData = () => {
    if (!legalDocument) return null

    return {
      '@context': 'https://schema.org',
      '@type': 'LegalDocument',
      name: legalDocument.title,
      description: description,
      url: fullCanonicalUrl,
      datePublished: legalDocument.issueDate,
      dateModified: legalDocument.effectiveDate || legalDocument.issueDate,
      publisher: {
        '@type': 'Organization',
        name: legalDocument.issuingAuthority,
        url: siteUrl
      },
      inLanguage: 'vi-VN',
      isPartOf: {
        '@type': 'WebSite',
        name: 'VietLaw.AI',
        url: siteUrl
      },
      about: {
        '@type': 'Thing',
        name: 'Vietnamese Law',
        description: 'Pháp luật Việt Nam'
      },
      additionalProperty: [
        {
          '@type': 'PropertyValue',
          name: 'documentType',
          value: legalDocument.documentType
        },
        {
          '@type': 'PropertyValue',
          name: 'documentNumber',
          value: legalDocument.documentNumber
        },
        {
          '@type': 'PropertyValue',
          name: 'status',
          value: legalDocument.status
        }
      ]
    }
  }

  // Generate breadcrumb structured data
  const generateBreadcrumbStructuredData = () => {
    if (breadcrumbs.length === 0) return null

    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbs.map((crumb, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: crumb.name,
        item: crumb.url.startsWith('http') ? crumb.url : `${siteUrl}${crumb.url}`
      }))
    }
  }

  // Generate organization structured data
  const generateOrganizationStructuredData = () => ({
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'VietLaw.AI',
    url: siteUrl,
    logo: `${siteUrl}/assets/logo.png`,
    description: 'Hệ thống AI tiên tiến cho pháp luật Việt Nam',
    foundingDate: '2024',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+84-xxx-xxx-xxx',
      contactType: 'customer service',
      availableLanguage: ['Vietnamese', 'English']
    },
    sameAs: [
      'https://linkedin.com/company/vietlaw-ai',
      'https://facebook.com/vietlaw.ai'
    ],
    areaServed: {
      '@type': 'Country',
      name: 'Vietnam'
    },
    serviceType: 'Legal Technology Services'
  })

  // Generate website structured data
  const generateWebsiteStructuredData = () => ({
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'VietLaw.AI',
    url: siteUrl,
    description: description,
    inLanguage: 'vi-VN',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${siteUrl}/search?q={search_term_string}`
      },
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: 'VietLaw.AI',
      url: siteUrl
    }
  })

  // Combine all structured data
  const allStructuredData = [
    generateOrganizationStructuredData(),
    generateWebsiteStructuredData(),
    ...(legalDocument ? [generateLegalDocumentStructuredData()] : []),
    ...(breadcrumbs.length > 0 ? [generateBreadcrumbStructuredData()] : []),
    ...(structuredData ? [structuredData] : [])
  ].filter(Boolean)

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <link rel="canonical" href={fullCanonicalUrl} />
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      {!noIndex && <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />}
      
      {/* Language and Region */}
      <meta name="language" content="Vietnamese" />
      <meta name="geo.region" content="VN" />
      <meta name="geo.country" content="Vietnam" />
      
      {/* Open Graph */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={fullCanonicalUrl} />
      <meta property="og:image" content={fullOgImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="VietLaw.AI" />
      <meta property="og:locale" content="vi_VN" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullOgImage} />
      <meta name="twitter:site" content="@VietLawAI" />
      <meta name="twitter:creator" content="@VietLawAI" />
      
      {/* Additional Meta Tags for Legal Content */}
      <meta name="subject" content="Vietnamese Legal Technology" />
      <meta name="copyright" content="VietLaw.AI" />
      <meta name="author" content="VietLaw.AI Team" />
      <meta name="designer" content="VietLaw.AI" />
      <meta name="reply-to" content="<EMAIL>" />
      <meta name="owner" content="VietLaw.AI" />
      <meta name="url" content={fullCanonicalUrl} />
      <meta name="identifier-URL" content={fullCanonicalUrl} />
      <meta name="directory" content="submission" />
      <meta name="category" content="Legal Technology, AI, Vietnamese Law" />
      <meta name="coverage" content="Worldwide" />
      <meta name="distribution" content="Global" />
      <meta name="rating" content="General" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Legal Document Specific Meta Tags */}
      {legalDocument && (
        <>
          <meta name="document.type" content={legalDocument.documentType} />
          <meta name="document.number" content={legalDocument.documentNumber} />
          <meta name="document.authority" content={legalDocument.issuingAuthority} />
          <meta name="document.date" content={legalDocument.issueDate} />
          <meta name="document.status" content={legalDocument.status} />
          {legalDocument.effectiveDate && (
            <meta name="document.effective_date" content={legalDocument.effectiveDate} />
          )}
        </>
      )}
      
      {/* Alternate Languages */}
      {alternateLanguages.map(({ lang, url }) => (
        <link key={lang} rel="alternate" hrefLang={lang} href={url} />
      ))}
      
      {/* Structured Data */}
      {allStructuredData.map((data, index) => (
        <script key={index} type="application/ld+json">
          {JSON.stringify(data)}
        </script>
      ))}
      
      {/* Preconnect to External Domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://api.vietlaw.ai" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//api.vietlaw.ai" />
      
      {/* Favicon and App Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      <meta name="theme-color" content="#1976d2" />
      
      {/* Mobile Optimization */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="VietLaw.AI" />
      
      {/* Performance Hints */}
      <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
      
      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
      
      {/* Cache Control */}
      <meta httpEquiv="Cache-Control" content="public, max-age=31536000" />
      
      {/* Legal and Compliance */}
      <meta name="rating" content="general" />
      <meta name="distribution" content="global" />
      <meta name="classification" content="Legal Technology" />
    </Helmet>
  )
}

export default SEOHead
