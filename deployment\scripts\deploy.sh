#!/bin/bash

# VietLaw.AI Deployment Script
# Automated deployment for multiple environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Default values
ENVIRONMENT="development"
PLATFORM="docker"
SKIP_TESTS=false
SKIP_BUILD=false
BACKUP_DB=false
ROLLBACK=false

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    cat << EOF
VietLaw.AI Deployment Script

Usage: $0 [OPTIONS]

Options:
    -e, --environment ENV    Target environment (development|staging|production)
    -p, --platform PLATFORM Deployment platform (docker|netlify|aws|gcp)
    -t, --skip-tests        Skip running tests
    -b, --skip-build        Skip building application
    -d, --backup-db         Create database backup before deployment
    -r, --rollback VERSION  Rollback to specific version
    -h, --help              Show this help message

Examples:
    $0 -e production -p aws -d
    $0 -e staging -p docker --skip-tests
    $0 --rollback v1.2.3

EOF
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check required tools
    local required_tools=("docker" "docker-compose" "node" "python3" "git")
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is required but not installed"
            exit 1
        fi
    done
    
    # Check environment file
    if [[ ! -f "$PROJECT_ROOT/.env.$ENVIRONMENT" ]]; then
        log_error "Environment file .env.$ENVIRONMENT not found"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

load_environment() {
    log_info "Loading environment configuration for $ENVIRONMENT..."
    
    # Load environment variables
    if [[ -f "$PROJECT_ROOT/.env.$ENVIRONMENT" ]]; then
        export $(cat "$PROJECT_ROOT/.env.$ENVIRONMENT" | grep -v '^#' | xargs)
    fi
    
    # Set platform-specific variables
    case $PLATFORM in
        "netlify")
            export DEPLOY_TARGET="netlify"
            ;;
        "aws")
            export DEPLOY_TARGET="aws"
            export AWS_REGION="${AWS_REGION:-ap-southeast-1}"
            ;;
        "gcp")
            export DEPLOY_TARGET="gcp"
            export GCP_PROJECT="${GCP_PROJECT:-vietlaw-ai}"
            ;;
        "docker")
            export DEPLOY_TARGET="docker"
            ;;
    esac
    
    log_success "Environment loaded: $ENVIRONMENT on $PLATFORM"
}

run_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_warning "Skipping tests"
        return 0
    fi
    
    log_info "Running tests..."
    
    # Backend tests
    cd "$PROJECT_ROOT/backend"
    python -m pytest tests/ -v --cov=app --cov-report=term-missing
    
    # Frontend tests
    cd "$PROJECT_ROOT/frontend"
    npm test -- --coverage --watchAll=false
    
    # Integration tests
    cd "$PROJECT_ROOT"
    python -m pytest tests/integration/ -v
    
    log_success "All tests passed"
}

build_application() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        log_warning "Skipping build"
        return 0
    fi
    
    log_info "Building application..."
    
    # Build frontend
    cd "$PROJECT_ROOT/frontend"
    npm ci
    npm run build
    
    # Build backend Docker image
    cd "$PROJECT_ROOT"
    docker build -t vietlaw-backend:$TIMESTAMP ./backend
    docker tag vietlaw-backend:$TIMESTAMP vietlaw-backend:latest
    
    # Build frontend Docker image
    docker build -t vietlaw-frontend:$TIMESTAMP ./frontend
    docker tag vietlaw-frontend:$TIMESTAMP vietlaw-frontend:latest
    
    log_success "Application built successfully"
}

backup_database() {
    if [[ "$BACKUP_DB" != "true" ]]; then
        return 0
    fi
    
    log_info "Creating database backup..."
    
    local backup_file="backup_${ENVIRONMENT}_${TIMESTAMP}.sql"
    local backup_path="$PROJECT_ROOT/backups/$backup_file"
    
    mkdir -p "$PROJECT_ROOT/backups"
    
    # Create backup based on environment
    case $ENVIRONMENT in
        "production")
            pg_dump "$DATABASE_URL" > "$backup_path"
            ;;
        "staging")
            pg_dump "$STAGING_DATABASE_URL" > "$backup_path"
            ;;
        *)
            docker-compose exec -T postgres pg_dump -U postgres vietlaw > "$backup_path"
            ;;
    esac
    
    # Compress backup
    gzip "$backup_path"
    
    log_success "Database backup created: ${backup_file}.gz"
}

deploy_docker() {
    log_info "Deploying to Docker..."
    
    cd "$PROJECT_ROOT"
    
    # Stop existing containers
    docker-compose down
    
    # Start services
    docker-compose -f docker-compose.yml -f "docker-compose.$ENVIRONMENT.yml" up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Run health checks
    if ! curl -f http://localhost:8000/health; then
        log_error "Backend health check failed"
        exit 1
    fi
    
    if ! curl -f http://localhost:3000; then
        log_error "Frontend health check failed"
        exit 1
    fi
    
    log_success "Docker deployment completed"
}

deploy_netlify() {
    log_info "Deploying to Netlify..."
    
    cd "$PROJECT_ROOT/frontend"
    
    # Install Netlify CLI if not present
    if ! command -v netlify &> /dev/null; then
        npm install -g netlify-cli
    fi
    
    # Deploy to Netlify
    if [[ "$ENVIRONMENT" == "production" ]]; then
        netlify deploy --prod --dir=dist
    else
        netlify deploy --dir=dist
    fi
    
    log_success "Netlify deployment completed"
}

deploy_aws() {
    log_info "Deploying to AWS..."
    
    cd "$PROJECT_ROOT/deployment/aws"
    
    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is required for AWS deployment"
        exit 1
    fi
    
    # Deploy infrastructure with Terraform
    cd terraform
    terraform init
    terraform plan -var-file="$ENVIRONMENT.tfvars"
    terraform apply -var-file="$ENVIRONMENT.tfvars" -auto-approve
    
    # Build and push Docker images to ECR
    aws ecr get-login-password --region "$AWS_REGION" | docker login --username AWS --password-stdin "$ECR_REPOSITORY"
    
    docker tag vietlaw-backend:latest "$ECR_REPOSITORY/vietlaw-backend:$TIMESTAMP"
    docker push "$ECR_REPOSITORY/vietlaw-backend:$TIMESTAMP"
    
    # Update ECS service
    aws ecs update-service \
        --cluster vietlaw-cluster \
        --service vietlaw-backend \
        --force-new-deployment \
        --region "$AWS_REGION"
    
    # Deploy frontend to S3 and CloudFront
    cd "$PROJECT_ROOT/frontend"
    aws s3 sync dist/ "s3://$S3_BUCKET" --delete
    aws cloudfront create-invalidation --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" --paths "/*"
    
    log_success "AWS deployment completed"
}

deploy_gcp() {
    log_info "Deploying to Google Cloud Platform..."
    
    cd "$PROJECT_ROOT/deployment/gcp"
    
    # Check gcloud CLI
    if ! command -v gcloud &> /dev/null; then
        log_error "Google Cloud CLI is required for GCP deployment"
        exit 1
    fi
    
    # Set project
    gcloud config set project "$GCP_PROJECT"
    
    # Build and deploy backend to Cloud Run
    cd "$PROJECT_ROOT/backend"
    gcloud builds submit --tag "gcr.io/$GCP_PROJECT/vietlaw-backend:$TIMESTAMP"
    gcloud run deploy vietlaw-backend \
        --image "gcr.io/$GCP_PROJECT/vietlaw-backend:$TIMESTAMP" \
        --platform managed \
        --region "$GCP_REGION" \
        --allow-unauthenticated
    
    # Deploy frontend to Firebase Hosting
    cd "$PROJECT_ROOT/frontend"
    npm install -g firebase-tools
    firebase deploy --project "$GCP_PROJECT"
    
    log_success "GCP deployment completed"
}

rollback_deployment() {
    log_info "Rolling back to version $ROLLBACK..."
    
    case $PLATFORM in
        "docker")
            docker-compose down
            docker tag "vietlaw-backend:$ROLLBACK" vietlaw-backend:latest
            docker tag "vietlaw-frontend:$ROLLBACK" vietlaw-frontend:latest
            docker-compose up -d
            ;;
        "aws")
            # Rollback ECS service
            aws ecs update-service \
                --cluster vietlaw-cluster \
                --service vietlaw-backend \
                --task-definition "vietlaw-backend:$ROLLBACK" \
                --region "$AWS_REGION"
            ;;
        "gcp")
            # Rollback Cloud Run service
            gcloud run deploy vietlaw-backend \
                --image "gcr.io/$GCP_PROJECT/vietlaw-backend:$ROLLBACK" \
                --platform managed \
                --region "$GCP_REGION"
            ;;
    esac
    
    log_success "Rollback completed"
}

main() {
    log_info "Starting VietLaw.AI deployment..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Platform: $PLATFORM"
    log_info "Timestamp: $TIMESTAMP"
    
    if [[ "$ROLLBACK" != "false" ]]; then
        rollback_deployment
        return 0
    fi
    
    check_prerequisites
    load_environment
    backup_database
    run_tests
    build_application
    
    case $PLATFORM in
        "docker")
            deploy_docker
            ;;
        "netlify")
            deploy_netlify
            ;;
        "aws")
            deploy_aws
            ;;
        "gcp")
            deploy_gcp
            ;;
        *)
            log_error "Unknown platform: $PLATFORM"
            exit 1
            ;;
    esac
    
    log_success "Deployment completed successfully!"
    log_info "Deployment timestamp: $TIMESTAMP"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        -t|--skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        -b|--skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -d|--backup-db)
            BACKUP_DB=true
            shift
            ;;
        -r|--rollback)
            ROLLBACK="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    log_error "Invalid environment: $ENVIRONMENT"
    exit 1
fi

# Validate platform
if [[ ! "$PLATFORM" =~ ^(docker|netlify|aws|gcp)$ ]]; then
    log_error "Invalid platform: $PLATFORM"
    exit 1
fi

# Run main function
main
