"""
VietLaw.AI Analysis API Endpoints
Risk analysis, document comparison, and legal analysis endpoints
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_

from app.core.database import get_db
from app.core.security import get_current_user
from app.core.rate_limiting import rate_limit
from app.models.user import User
from app.models.analysis import RiskAnalysis, DocumentComparison, UserQuery
from app.models.document import LegalDocument
from app.schemas.analysis import (
    RiskAnalysisRequest, RiskAnalysisResponse,
    DocumentComparisonRequest, DocumentComparisonResponse,
    AnalysisHistoryResponse, AnalysisStatsResponse
)
from app.services.risk_analysis import risk_analysis_service
from app.services.document_comparison import document_comparison_service
from app.services.vietnamese_nlp import vietnamese_nlp

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/risk-analysis", response_model=RiskAnalysisResponse)
@rate_limit(requests=20, window=3600)  # 20 requests per hour
async def create_risk_analysis(
    request: RiskAnalysisRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new risk analysis for legal text
    
    Requires professional or enterprise subscription
    """
    try:
        # Check user permissions
        if not current_user.can_access_feature("risk_analysis"):
            raise HTTPException(
                status_code=403,
                detail="Risk analysis requires professional or enterprise subscription"
            )
        
        # Create analysis record
        analysis = RiskAnalysis(
            user_id=current_user.id,
            document_id=request.document_id,
            analysis_text=request.text,
            analysis_type=request.analysis_type,
            overall_risk_score=0.0,  # Will be updated by background task
            risk_level="medium",     # Will be updated by background task
            confidence_score=0.0,    # Will be updated by background task
            status="pending",
            analysis_parameters=request.parameters or {}
        )
        
        db.add(analysis)
        await db.flush()
        await db.refresh(analysis)
        
        # Start background analysis
        background_tasks.add_task(
            perform_risk_analysis,
            str(analysis.id),
            request.text,
            request.analysis_type,
            request.parameters or {}
        )
        
        await db.commit()
        
        logger.info(f"Created risk analysis {analysis.id} for user {current_user.id}")
        
        return RiskAnalysisResponse(
            id=str(analysis.id),
            status=analysis.status,
            analysis_type=analysis.analysis_type,
            created_at=analysis.created_at,
            message="Risk analysis started. Check status for results."
        )
        
    except Exception as e:
        logger.error(f"Error creating risk analysis: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create risk analysis")


@router.get("/risk-analysis/{analysis_id}", response_model=RiskAnalysisResponse)
async def get_risk_analysis(
    analysis_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get risk analysis results by ID"""
    try:
        # Get analysis
        result = await db.execute(
            select(RiskAnalysis)
            .where(
                and_(
                    RiskAnalysis.id == analysis_id,
                    RiskAnalysis.user_id == current_user.id
                )
            )
        )
        analysis = result.scalar_one_or_none()
        
        if not analysis:
            raise HTTPException(status_code=404, detail="Risk analysis not found")
        
        return RiskAnalysisResponse.from_orm(analysis)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting risk analysis {analysis_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get risk analysis")


@router.post("/document-comparison", response_model=DocumentComparisonResponse)
@rate_limit(requests=10, window=3600)  # 10 requests per hour
async def create_document_comparison(
    request: DocumentComparisonRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Compare two legal documents
    
    Requires professional or enterprise subscription
    """
    try:
        # Check user permissions
        if not current_user.can_access_feature("document_comparison"):
            raise HTTPException(
                status_code=403,
                detail="Document comparison requires professional or enterprise subscription"
            )
        
        # Validate documents exist if IDs provided
        if request.document1_id:
            doc1_result = await db.execute(
                select(LegalDocument).where(LegalDocument.id == request.document1_id)
            )
            if not doc1_result.scalar_one_or_none():
                raise HTTPException(status_code=404, detail="Document 1 not found")
        
        if request.document2_id:
            doc2_result = await db.execute(
                select(LegalDocument).where(LegalDocument.id == request.document2_id)
            )
            if not doc2_result.scalar_one_or_none():
                raise HTTPException(status_code=404, detail="Document 2 not found")
        
        # Create comparison record
        comparison = DocumentComparison(
            user_id=current_user.id,
            document1_id=request.document1_id,
            document2_id=request.document2_id,
            document1_text=request.document1_text,
            document2_text=request.document2_text,
            similarity_score=0.0,  # Will be updated by background task
            comparison_type=request.comparison_type,
            status="pending",
            comparison_parameters=request.parameters or {}
        )
        
        db.add(comparison)
        await db.flush()
        await db.refresh(comparison)
        
        # Start background comparison
        background_tasks.add_task(
            perform_document_comparison,
            str(comparison.id),
            request
        )
        
        await db.commit()
        
        logger.info(f"Created document comparison {comparison.id} for user {current_user.id}")
        
        return DocumentComparisonResponse(
            id=str(comparison.id),
            status=comparison.status,
            comparison_type=comparison.comparison_type,
            created_at=comparison.created_at,
            message="Document comparison started. Check status for results."
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating document comparison: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create document comparison")


@router.get("/document-comparison/{comparison_id}", response_model=DocumentComparisonResponse)
async def get_document_comparison(
    comparison_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get document comparison results by ID"""
    try:
        # Get comparison
        result = await db.execute(
            select(DocumentComparison)
            .where(
                and_(
                    DocumentComparison.id == comparison_id,
                    DocumentComparison.user_id == current_user.id
                )
            )
        )
        comparison = result.scalar_one_or_none()
        
        if not comparison:
            raise HTTPException(status_code=404, detail="Document comparison not found")
        
        return DocumentComparisonResponse.from_orm(comparison)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting document comparison {comparison_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get document comparison")


@router.get("/history", response_model=AnalysisHistoryResponse)
async def get_analysis_history(
    analysis_type: Optional[str] = Query(None, description="Filter by analysis type"),
    limit: int = Query(20, ge=1, le=100, description="Number of results to return"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user's analysis history"""
    try:
        # Build query for risk analyses
        risk_query = select(RiskAnalysis).where(RiskAnalysis.user_id == current_user.id)
        if analysis_type and analysis_type in ["compliance", "contract", "regulatory", "general"]:
            risk_query = risk_query.where(RiskAnalysis.analysis_type == analysis_type)
        
        # Build query for document comparisons
        comparison_query = select(DocumentComparison).where(DocumentComparison.user_id == current_user.id)
        if analysis_type and analysis_type in ["semantic", "structural", "legal", "full"]:
            comparison_query = comparison_query.where(DocumentComparison.comparison_type == analysis_type)
        
        # Get risk analyses
        risk_result = await db.execute(
            risk_query.order_by(RiskAnalysis.created_at.desc())
            .limit(limit // 2)
            .offset(offset // 2)
        )
        risk_analyses = risk_result.scalars().all()
        
        # Get document comparisons
        comparison_result = await db.execute(
            comparison_query.order_by(DocumentComparison.created_at.desc())
            .limit(limit // 2)
            .offset(offset // 2)
        )
        comparisons = comparison_result.scalars().all()
        
        # Get total counts
        risk_count_result = await db.execute(
            select(func.count(RiskAnalysis.id)).where(RiskAnalysis.user_id == current_user.id)
        )
        total_risk_analyses = risk_count_result.scalar()
        
        comparison_count_result = await db.execute(
            select(func.count(DocumentComparison.id)).where(DocumentComparison.user_id == current_user.id)
        )
        total_comparisons = comparison_count_result.scalar()
        
        return AnalysisHistoryResponse(
            risk_analyses=[analysis.to_dict(include_details=False) for analysis in risk_analyses],
            document_comparisons=[comp.to_dict(include_content=False) for comp in comparisons],
            total_risk_analyses=total_risk_analyses,
            total_comparisons=total_comparisons,
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        logger.error(f"Error getting analysis history: {e}")
        raise HTTPException(status_code=500, detail="Failed to get analysis history")


@router.get("/stats", response_model=AnalysisStatsResponse)
async def get_analysis_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user's analysis statistics"""
    try:
        # Get risk analysis stats
        risk_stats_result = await db.execute(
            select(
                func.count(RiskAnalysis.id).label("total"),
                func.count(RiskAnalysis.id).filter(RiskAnalysis.status == "completed").label("completed"),
                func.avg(RiskAnalysis.overall_risk_score).label("avg_risk_score")
            ).where(RiskAnalysis.user_id == current_user.id)
        )
        risk_stats = risk_stats_result.first()
        
        # Get comparison stats
        comparison_stats_result = await db.execute(
            select(
                func.count(DocumentComparison.id).label("total"),
                func.count(DocumentComparison.id).filter(DocumentComparison.status == "completed").label("completed"),
                func.avg(DocumentComparison.similarity_score).label("avg_similarity")
            ).where(DocumentComparison.user_id == current_user.id)
        )
        comparison_stats = comparison_stats_result.first()
        
        # Get recent activity
        recent_activity = []
        
        # Recent risk analyses
        recent_risk_result = await db.execute(
            select(RiskAnalysis)
            .where(RiskAnalysis.user_id == current_user.id)
            .order_by(RiskAnalysis.created_at.desc())
            .limit(5)
        )
        for analysis in recent_risk_result.scalars():
            recent_activity.append({
                "type": "risk_analysis",
                "id": str(analysis.id),
                "created_at": analysis.created_at.isoformat(),
                "status": analysis.status,
                "analysis_type": analysis.analysis_type
            })
        
        # Recent comparisons
        recent_comp_result = await db.execute(
            select(DocumentComparison)
            .where(DocumentComparison.user_id == current_user.id)
            .order_by(DocumentComparison.created_at.desc())
            .limit(5)
        )
        for comparison in recent_comp_result.scalars():
            recent_activity.append({
                "type": "document_comparison",
                "id": str(comparison.id),
                "created_at": comparison.created_at.isoformat(),
                "status": comparison.status,
                "comparison_type": comparison.comparison_type
            })
        
        # Sort by date
        recent_activity.sort(key=lambda x: x["created_at"], reverse=True)
        recent_activity = recent_activity[:10]
        
        return AnalysisStatsResponse(
            total_risk_analyses=risk_stats.total or 0,
            completed_risk_analyses=risk_stats.completed or 0,
            avg_risk_score=float(risk_stats.avg_risk_score) if risk_stats.avg_risk_score else 0.0,
            total_comparisons=comparison_stats.total or 0,
            completed_comparisons=comparison_stats.completed or 0,
            avg_similarity_score=float(comparison_stats.avg_similarity) if comparison_stats.avg_similarity else 0.0,
            recent_activity=recent_activity
        )
        
    except Exception as e:
        logger.error(f"Error getting analysis stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get analysis statistics")


async def perform_risk_analysis(
    analysis_id: str,
    text: str,
    analysis_type: str,
    parameters: Dict
):
    """Background task to perform risk analysis"""
    try:
        # Perform the actual risk analysis
        result = await risk_analysis_service.analyze_risk(
            text=text,
            analysis_type=analysis_type,
            parameters=parameters
        )
        
        # Update analysis record
        async with get_db_session_context() as db:
            analysis_result = await db.execute(
                select(RiskAnalysis).where(RiskAnalysis.id == analysis_id)
            )
            analysis = analysis_result.scalar_one_or_none()
            
            if analysis:
                analysis.overall_risk_score = result["overall_risk_score"]
                analysis.risk_level = result["risk_level"]
                analysis.confidence_score = result["confidence_score"]
                analysis.risk_factors = result["risk_factors"]
                analysis.recommendations = result["recommendations"]
                analysis.legal_issues = result["legal_issues"]
                analysis.compliance_gaps = result["compliance_gaps"]
                analysis.status = "completed"
                analysis.completed_at = datetime.utcnow()
                analysis.analysis_duration_ms = result.get("analysis_duration_ms")
                analysis.model_version = result.get("model_version")
                
                await db.commit()
                logger.info(f"Completed risk analysis {analysis_id}")
            
    except Exception as e:
        logger.error(f"Error in background risk analysis {analysis_id}: {e}")
        # Update status to failed
        async with get_db_session_context() as db:
            analysis_result = await db.execute(
                select(RiskAnalysis).where(RiskAnalysis.id == analysis_id)
            )
            analysis = analysis_result.scalar_one_or_none()
            if analysis:
                analysis.status = "failed"
                await db.commit()


async def perform_document_comparison(
    comparison_id: str,
    request: DocumentComparisonRequest
):
    """Background task to perform document comparison"""
    try:
        # Perform the actual document comparison
        result = await document_comparison_service.compare_documents(
            document1_id=request.document1_id,
            document2_id=request.document2_id,
            document1_text=request.document1_text,
            document2_text=request.document2_text,
            comparison_type=request.comparison_type,
            parameters=request.parameters or {}
        )
        
        # Update comparison record
        async with get_db_session_context() as db:
            comparison_result = await db.execute(
                select(DocumentComparison).where(DocumentComparison.id == comparison_id)
            )
            comparison = comparison_result.scalar_one_or_none()
            
            if comparison:
                comparison.similarity_score = result["similarity_score"]
                comparison.similarities = result["similarities"]
                comparison.differences = result["differences"]
                comparison.key_changes = result["key_changes"]
                comparison.impact_analysis = result["impact_analysis"]
                comparison.status = "completed"
                comparison.completed_at = datetime.utcnow()
                comparison.comparison_duration_ms = result.get("comparison_duration_ms")
                comparison.model_version = result.get("model_version")
                
                await db.commit()
                logger.info(f"Completed document comparison {comparison_id}")
            
    except Exception as e:
        logger.error(f"Error in background document comparison {comparison_id}: {e}")
        # Update status to failed
        async with get_db_session_context() as db:
            comparison_result = await db.execute(
                select(DocumentComparison).where(DocumentComparison.id == comparison_id)
            )
            comparison = comparison_result.scalar_one_or_none()
            if comparison:
                comparison.status = "failed"
                await db.commit()
