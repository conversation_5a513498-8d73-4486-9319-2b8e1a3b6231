# VietLaw.AI - Vietnamese Legal AI Assistant

A comprehensive RAG (Retrieval-Augmented Generation) system specifically designed for Vietnamese legal documents, targeting law firms, enterprises, and legal professionals.

## 🏗️ System Architecture

### Overall Architecture
![System Architecture](docs/architecture-overview.png)

### Key Components
- **Frontend**: React.js with mobile-responsive design
- **Backend**: Python/FastAPI with PostgreSQL + pgvector
- **AI/ML**: Vietnamese legal language models with confidence scoring
- **Deployment**: Multi-cloud support (Netlify, AWS, GCP)

## 🚀 Features

### Core Features
- **Semantic Search**: Context-aware search in Vietnamese with legal terminology understanding
- **Legal Risk Analysis**: Automated risk assessment scoring for legal scenarios
- **Real-time Updates**: Monitor and integrate new legal documents as they're published
- **Comparative Analysis**: Side-by-side comparison of legal provisions across different laws
- **Citation Tracking**: Automatic linking and tracing of legal references
- **Amendment Tracking**: Historical view of how laws have changed over time

### Business Integration
- Microsoft Office and Google Workspace add-ins
- REST APIs for third-party integrations
- Enterprise SSO support
- Tiered pricing model (B2B SaaS: $50-500/month, Enterprise: $2000-10000/month)

## 📁 Project Structure

```
vietlaw-ai/
├── frontend/                 # React.js frontend application
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── services/
│   │   └── utils/
│   ├── package.json
│   └── vite.config.js
├── backend/                  # FastAPI backend application
│   ├── app/
│   │   ├── api/
│   │   ├── core/
│   │   ├── models/
│   │   ├── services/
│   │   └── utils/
│   ├── requirements.txt
│   └── main.py
├── ai-models/               # AI/ML models and processing
│   ├── embeddings/
│   ├── nlp/
│   ├── risk-analysis/
│   └── vietnamese-processing/
├── database/                # Database schemas and migrations
│   ├── migrations/
│   ├── schemas/
│   └── seeds/
├── integrations/           # Office add-ins and integrations
│   ├── office-addins/
│   ├── google-workspace/
│   └── api-clients/
├── deployment/             # Deployment configurations
│   ├── netlify/
│   ├── aws/
│   ├── gcp/
│   └── docker/
├── docs/                   # Documentation
│   ├── api/
│   ├── architecture/
│   └── user-guide/
├── tests/                  # Test suites
│   ├── backend/
│   ├── frontend/
│   └── integration/
├── scripts/                # Utility scripts
│   ├── data-ingestion/
│   ├── deployment/
│   └── maintenance/
├── .env.example
├── .gitignore
├── docker-compose.yml
└── README.md
```

## 🏗️ Architecture

VietLaw.AI follows a modern microservices architecture designed for scalability, performance, and Vietnamese legal content processing:

### Backend (FastAPI + Python)
- **API Gateway**: FastAPI with automatic OpenAPI documentation and rate limiting
- **Authentication**: JWT-based auth with enterprise SSO support (SAML, OAuth, OIDC)
- **Database**: PostgreSQL with pgvector extension for vector storage
- **Caching**: Redis for session management, query caching, and real-time features
- **Search Engine**: Advanced semantic search with Vietnamese NLP processing
- **Background Tasks**: Celery for document processing, analysis, and batch operations
- **Vector Operations**: Optimized similarity search with pgvector and custom algorithms
- **Legal Processing**: Specialized services for Vietnamese legal terminology and document analysis

### Frontend (React + TypeScript)
- **Framework**: React 18 with TypeScript and Vite for fast development
- **State Management**: Zustand for global state with persistence
- **UI Components**: Custom component library with Tailwind CSS and responsive design
- **Data Fetching**: React Query for server state management and caching
- **Routing**: React Router with protected routes and role-based access
- **PWA Support**: Progressive Web App capabilities for mobile experience
- **SEO Optimization**: Comprehensive meta tags and structured data for Vietnamese content

### AI/ML Pipeline
- **Vietnamese NLP**: Advanced processing with underthesea, pyvi, and custom legal terminology
- **Embeddings**: SentenceTransformer models optimized for Vietnamese legal text
- **Vector Database**: pgvector with optimized indexing for fast similarity search
- **Legal Analysis**: Custom algorithms for risk assessment, compliance checking, and document comparison
- **Text Chunking**: Intelligent document segmentation for optimal RAG performance
- **Citation Tracking**: Automated extraction and tracking of legal references and amendments

### Integration Layer
- **Microsoft Office**: Word, Excel, PowerPoint add-ins for legal document analysis
- **Google Workspace**: Docs, Sheets, Slides integration with contextual legal insights
- **Enterprise SSO**: Support for SAML, OAuth 2.0, and OpenID Connect
- **Third-party APIs**: Integration with legal databases and government portals
- **Webhook Support**: Real-time notifications and data synchronization

### Infrastructure & DevOps
- **Containerization**: Docker with optimized multi-stage builds and docker-compose
- **Deployment**: Multi-cloud support (Netlify, AWS, GCP) with Terraform infrastructure
- **Monitoring**: Prometheus metrics, Grafana dashboards, and custom alerting
- **Logging**: Structured logging with ELK stack integration
- **CI/CD**: GitHub Actions with automated testing, security scanning, and deployment
- **Security**: HTTPS, CORS, rate limiting, input validation, and security headers
- **Performance**: CDN integration, image optimization, and caching strategies

## 🛠️ Quick Start

### Prerequisites
- Node.js 18+
- Python 3.9+
- PostgreSQL 14+
- Redis 6+

### Installation

#### Option 1: Automated Installation (Recommended)

**For Linux/macOS:**
```bash
git clone https://github.com/HectorTa1989/vietlaw-ai.git
cd vietlaw-ai
chmod +x scripts/install-dependencies.sh
./scripts/install-dependencies.sh
```

**For Windows:**
```cmd
git clone https://github.com/HectorTa1989/vietlaw-ai.git
cd vietlaw-ai
scripts\install-dependencies.bat
```

#### Option 2: Manual Installation

1. **Clone the repository**
```bash
git clone https://github.com/HectorTa1989/vietlaw-ai.git
cd vietlaw-ai
```

2. **Setup Backend**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Upgrade pip first
python -m pip install --upgrade pip

# Install PyTorch with CPU support for compatibility
pip install "torch>=2.2.0,<2.8.0" --index-url https://download.pytorch.org/whl/cpu

# Install other dependencies
pip install -r requirements.txt
```

3. **Setup Frontend**
```bash
cd frontend
npm cache clean --force
npm install

# If you encounter dependency conflicts, try:
npm install --legacy-peer-deps
```

4. **Setup Database**
```bash
# Install PostgreSQL with pgvector extension
# Run database migrations
cd database
python migrate.py
```

5. **Environment Configuration**
```bash
cp .env.example .env
# Edit .env with your configuration
```

6. **Start Development Servers**
```bash
# Terminal 1: Backend
cd backend
source venv/bin/activate  # On Windows: venv\Scripts\activate
uvicorn main:app --reload

# Terminal 2: Frontend
cd frontend
npm run dev
```

#### Troubleshooting Common Issues

**Setuptools Build Meta Error:**
```
ERROR: Cannot import 'setuptools.build_meta'
```
This is the most common error. Fix it with:
```bash
# Windows
scripts\fix-setuptools-error.bat

# Linux/macOS
chmod +x scripts/fix-setuptools-error.sh
./scripts/fix-setuptools-error.sh
```

**PyTorch Installation Issues:**
```bash
# If you get torch version errors, run:
cd backend
source venv/bin/activate
pip uninstall torch torchvision torchaudio -y
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

**npm Dependency Conflicts:**
```bash
# If you get npm dependency errors, run:
cd frontend
rm -rf node_modules package-lock.json
npm cache clean --force
npm install --legacy-peer-deps
```

**Fix All Issues Automatically:**
```bash
# Linux/macOS
./scripts/install-dependencies.sh --fix-issues

# Windows
scripts\install-dependencies.bat --fix-issues
```

## 🌐 Deployment

VietLaw.AI supports multiple deployment strategies for different environments and requirements.

### Docker Deployment (Recommended for Development)

1. **Using Docker Compose**
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

2. **Individual Service Deployment**
```bash
# Backend only
docker-compose up -d backend postgres redis

# Frontend only
docker-compose up -d frontend

# Database only
docker-compose up -d postgres redis
```

### Netlify Deployment (Frontend)

1. **Automatic Deployment**
```bash
# Connect your GitHub repository to Netlify
# Netlify will automatically deploy on push to main branch

# Build settings:
# Build command: npm run build
# Publish directory: dist
# Node version: 18
```

2. **Manual Deployment**
```bash
cd frontend
npm run build
npx netlify deploy --prod --dir=dist
```

3. **Environment Variables**
```bash
# Set in Netlify dashboard:
REACT_APP_API_URL=https://api.vietlaw.ai
REACT_APP_ENVIRONMENT=production
REACT_APP_SENTRY_DSN=your_sentry_dsn
```

### AWS Deployment (Full Stack)

1. **Infrastructure Setup with Terraform**
```bash
cd deployment/aws/terraform

# Initialize Terraform
terraform init

# Plan deployment
terraform plan -var-file="production.tfvars"

# Apply infrastructure
terraform apply -var-file="production.tfvars"
```

2. **Application Deployment**
```bash
# Build and push Docker images
docker build -t vietlaw-backend ./backend
docker tag vietlaw-backend:latest your-ecr-repo/vietlaw-backend:latest
docker push your-ecr-repo/vietlaw-backend:latest

# Deploy to ECS
aws ecs update-service --cluster vietlaw-cluster --service vietlaw-backend --force-new-deployment
```

3. **Database Migration**
```bash
# Run migrations on RDS
python backend/migrate.py --env=production
```

### Google Cloud Platform Deployment

1. **Setup GCP Project**
```bash
# Create project and enable APIs
gcloud projects create vietlaw-ai-prod
gcloud config set project vietlaw-ai-prod

# Enable required APIs
gcloud services enable compute.googleapis.com
gcloud services enable container.googleapis.com
gcloud services enable sql.googleapis.com
```

2. **Deploy to Cloud Run**
```bash
# Build and deploy backend
gcloud builds submit --tag gcr.io/vietlaw-ai-prod/backend ./backend
gcloud run deploy vietlaw-backend --image gcr.io/vietlaw-ai-prod/backend --platform managed

# Deploy frontend to Firebase Hosting
cd frontend
npm run build
firebase deploy --project vietlaw-ai-prod
```

### Production Environment Setup

1. **Environment Variables**
```bash
# Backend (.env)
DATABASE_URL=********************************/vietlaw_prod
REDIS_URL=redis://host:6379/0
SECRET_KEY=your-secret-key
OPENAI_API_KEY=your-openai-key
SENTRY_DSN=your-sentry-dsn
ENVIRONMENT=production

# Frontend
REACT_APP_API_URL=https://api.vietlaw.ai
REACT_APP_ENVIRONMENT=production
REACT_APP_SENTRY_DSN=your-sentry-dsn
```

2. **SSL Certificate Setup**
```bash
# Using Let's Encrypt with Certbot
sudo certbot --nginx -d api.vietlaw.ai
sudo certbot --nginx -d vietlaw.ai
```

3. **Database Backup**
```bash
# Setup automated backups
pg_dump vietlaw_prod > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore from backup
psql vietlaw_prod < backup_file.sql
```

### Monitoring and Logging

1. **Prometheus and Grafana**
```bash
# Deploy monitoring stack
cd deployment/monitoring
docker-compose up -d prometheus grafana

# Access dashboards
# Grafana: http://localhost:3000 (admin/admin)
# Prometheus: http://localhost:9090
```

2. **Log Aggregation**
```bash
# ELK Stack deployment
cd deployment/logging
docker-compose up -d elasticsearch logstash kibana

# View logs at http://localhost:5601
```

### Health Checks and Monitoring

1. **Application Health**
```bash
# Backend health check
curl https://api.vietlaw.ai/health

# Database health check
curl https://api.vietlaw.ai/health/db

# Redis health check
curl https://api.vietlaw.ai/health/redis
```

2. **Performance Monitoring**
```bash
# Setup application performance monitoring
# Configure Sentry for error tracking
# Setup New Relic or DataDog for performance metrics
```
netlify deploy --prod
```

### AWS Deployment
```bash
cd deployment/aws
terraform init
terraform apply
```

### GCP Deployment
```bash
cd deployment/gcp
gcloud app deploy
```

## 📚 API Documentation

VietLaw.AI provides a comprehensive REST API for legal document search, analysis, and management.

### Base URL
```
Production: https://api.vietlaw.ai/v1
Development: http://localhost:8000/v1
```

### Authentication

All API requests require authentication using JWT tokens:

```bash
# Login to get access token
curl -X POST "https://api.vietlaw.ai/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Use token in subsequent requests
curl -X GET "https://api.vietlaw.ai/v1/search/semantic" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Core Endpoints

#### 1. Search API

**Semantic Search**
```bash
POST /v1/search/semantic
Content-Type: application/json
Authorization: Bearer {token}

{
  "query": "quyền và nghĩa vụ của người lao động",
  "limit": 20,
  "document_types": ["law", "decree"],
  "date_from": "2020-01-01",
  "date_to": "2024-12-31"
}
```

**Response:**
```json
{
  "results": [
    {
      "id": "doc_123",
      "title": "Luật Lao động số 45/2019/QH14",
      "summary": "Quy định về quyền và nghĩa vụ của người lao động...",
      "document_type": "law",
      "issuing_authority": "Quốc hội",
      "issue_date": "2019-11-20",
      "effective_date": "2021-01-01",
      "status": "active",
      "relevance_score": 0.95,
      "highlights": ["người lao động", "quyền và nghĩa vụ"]
    }
  ],
  "total_results": 156,
  "query_time": 0.15,
  "suggestions": ["Luật Lao động 2019", "quyền người lao động"]
}
```

#### 2. Document Analysis API

**Risk Assessment**
```bash
POST /v1/documents/{document_id}/analyze
Content-Type: application/json

{
  "analysis_type": "risk_assessment",
  "context": "employment_contract",
  "user_document": "base64_encoded_document"
}
```

**Response:**
```json
{
  "analysis_id": "analysis_123",
  "risk_score": 0.75,
  "risk_factors": [
    {
      "factor": "missing_termination_clause",
      "severity": "high",
      "description": "Hợp đồng thiếu điều khoản chấm dứt hợp đồng",
      "recommendation": "Bổ sung điều khoản về chấm dứt hợp đồng theo Điều 36 Luật Lao động"
    }
  ],
  "compliance_issues": [],
  "recommendations": []
}
```

### Rate Limiting

API requests are rate-limited based on subscription plan:

- **Basic**: 100 requests/hour
- **Professional**: 1,000 requests/hour
- **Enterprise**: 10,000 requests/hour

### Interactive Documentation

Full API documentation is available at:
- **Development**: http://localhost:8000/docs
- **Production**: https://api.vietlaw.ai/docs
- **OpenAPI JSON**: https://api.vietlaw.ai/openapi.json

## 🧪 Testing

```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test

# Integration tests
cd tests/integration
python -m pytest
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔗 Links

- **Website**: https://vietlaw.ai
- **API Documentation**: https://api.vietlaw.ai/docs
- **Support**: <EMAIL>
- **GitHub**: https://github.com/HectorTa1989/vietlaw-ai

## 📞 Contact

For enterprise inquiries and support:
- Email: <EMAIL>
- Phone: +84 (0) 123 456 789
