# VietLaw.AI Dependency Fixes

This document outlines the fixes applied to resolve dependency installation issues.

## Issues Identified

### 1. PyTorch Version Conflict
**Problem:** `torch==2.1.2` is not available in the current PyPI repository
**Error:** `ERROR: Could not find a version that satisfies the requirement torch==2.1.2`

**Solution:**
- Updated `backend/requirements.txt` to use flexible version range: `torch>=2.2.0,<2.8.0`
- This allows installation of any compatible PyTorch version from 2.2.0 to 2.7.x

### 2. Google Apps Script Package Issue
**Problem:** `google-apps-script@^1.7.1` package doesn't exist in npm registry
**Error:** `npm error notarget No matching version found for google-apps-script@^1.7.1`

**Solution:**
- Removed `google-apps-script` package from `frontend/package.json`
- This package was unnecessary as we're using Google Apps Script API directly through HTTP requests

### 3. Duplicate React Query Dependencies
**Problem:** Both `react-query` (legacy) and `@tanstack/react-query` (new) were listed
**Solution:**
- Removed legacy `react-query@^3.39.3` package
- Kept only `@tanstack/react-query@^5.8.4` (the modern version)

## Files Modified

### backend/requirements.txt
```diff
- torch==2.1.2
+ torch>=2.2.0,<2.8.0

# Removed duplicate httpx entry in testing section
- httpx==0.25.2  # (duplicate removed)
```

### frontend/package.json
```diff
- "react-query": "^3.39.3",
- "google-apps-script": "^1.7.1",
```

## New Installation Scripts

### 1. Automated Installation Script (Linux/macOS)
**File:** `scripts/install-dependencies.sh`
**Features:**
- Checks prerequisites (Python 3.9+, Node.js 18+)
- Creates Python virtual environment
- Installs PyTorch with CPU-only version for compatibility
- Handles npm dependency conflicts with `--legacy-peer-deps`
- Verifies installation
- Provides troubleshooting options

**Usage:**
```bash
chmod +x scripts/install-dependencies.sh
./scripts/install-dependencies.sh
```

### 2. Windows Installation Script
**File:** `scripts/install-dependencies.bat`
**Features:**
- Windows-compatible batch script
- Same functionality as Linux/macOS version
- Handles Windows-specific paths and commands

**Usage:**
```cmd
scripts\install-dependencies.bat
```

### 3. Dependency Verification Script
**File:** `scripts/verify-dependencies.py`
**Features:**
- Checks Python and Node.js versions
- Verifies all required packages are installed
- Tests basic functionality (Vietnamese NLP, embeddings)
- Provides detailed error reporting
- Cross-platform compatibility

**Usage:**
```bash
python scripts/verify-dependencies.py
```

## Installation Options

### Option 1: Automated Installation (Recommended)

**Linux/macOS:**
```bash
git clone https://github.com/HectorTa1989/vietlaw-ai.git
cd vietlaw-ai
./scripts/install-dependencies.sh
```

**Windows:**
```cmd
git clone https://github.com/HectorTa1989/vietlaw-ai.git
cd vietlaw-ai
scripts\install-dependencies.bat
```

### Option 2: Manual Installation with Fixes

**Backend:**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Upgrade pip first
python -m pip install --upgrade pip

# Install PyTorch with CPU support for compatibility
pip install "torch>=2.2.0,<2.8.0" --index-url https://download.pytorch.org/whl/cpu

# Install other dependencies
pip install -r requirements.txt
```

**Frontend:**
```bash
cd frontend
npm cache clean --force
npm install

# If dependency conflicts occur:
npm install --legacy-peer-deps
```

## Troubleshooting Common Issues

### PyTorch Installation Issues
```bash
cd backend
source venv/bin/activate
pip uninstall torch torchvision torchaudio -y
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

### npm Dependency Conflicts
```bash
cd frontend
rm -rf node_modules package-lock.json
npm cache clean --force
npm install --legacy-peer-deps
```

### Fix All Issues Automatically
```bash
# Linux/macOS
./scripts/install-dependencies.sh --fix-issues

# Windows
scripts\install-dependencies.bat --fix-issues
```

## Verification

After installation, verify everything works:

```bash
# Run verification script
python scripts/verify-dependencies.py

# Or manually test:
cd backend
source venv/bin/activate
python -c "import torch, fastapi, underthesea; print('Backend OK')"

cd ../frontend
npm run type-check
```

## Updated README

The main README.md has been updated with:
- Automated installation instructions
- Manual installation with fixes
- Troubleshooting section
- Common error solutions

## Benefits of These Fixes

1. **Compatibility:** Uses flexible version ranges for better compatibility
2. **Automation:** Reduces manual setup errors
3. **Cross-platform:** Works on Windows, macOS, and Linux
4. **Error Handling:** Provides clear error messages and solutions
5. **Verification:** Includes comprehensive testing of installation
6. **Troubleshooting:** Built-in fixes for common issues

## Next Steps

1. Test the installation scripts on different platforms
2. Update CI/CD pipeline to use the new installation process
3. Monitor for any new dependency conflicts
4. Consider pinning specific working versions for production deployments

## Support

If you encounter issues not covered by these fixes:

1. Run the verification script: `python scripts/verify-dependencies.py`
2. Check the troubleshooting section in README.md
3. Use the automated fix: `./scripts/install-dependencies.sh --fix-issues`
4. Open an issue on GitHub with the error details
