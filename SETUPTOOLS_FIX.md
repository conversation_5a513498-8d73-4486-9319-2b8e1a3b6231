# Fix for Setuptools Build Meta Error

## Error Description
```
pip._vendor.pyproject_hooks._impl.BackendUnavailable: Cannot import 'setuptools.build_meta'
```

This error occurs when the Python build tools (setuptools) are missing or corrupted in the virtual environment.

## Quick Fix (Windows)

### Option 1: Automated Fix Script
```cmd
scripts\fix-setuptools-error.bat
```

### Option 2: Manual Fix
```cmd
cd backend

# Remove corrupted virtual environment
rmdir /s /q venv

# Create fresh virtual environment
python -m venv venv

# Activate virtual environment
venv\Scripts\activate

# Install build tools FIRST (this is crucial)
python -m pip install --upgrade pip>=23.0
pip install --upgrade setuptools>=65.0 wheel>=0.38.0

# Install PyTorch with CPU support
pip install "torch>=2.2.0,<2.8.0" --index-url https://download.pytorch.org/whl/cpu

# Install core dependencies first
pip install fastapi uvicorn pydantic sqlalchemy alembic
pip install psycopg2-binary asyncpg pgvector redis aioredis
pip install sentence-transformers transformers numpy scikit-learn pandas
pip install underthesea pyvi

# Install remaining dependencies
pip install -r requirements.txt
```

## Quick Fix (Linux/macOS)

### Option 1: Automated Fix Script
```bash
chmod +x scripts/fix-setuptools-error.sh
./scripts/fix-setuptools-error.sh
```

### Option 2: Manual Fix
```bash
cd backend

# Remove corrupted virtual environment
rm -rf venv

# Create fresh virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Install build tools FIRST (this is crucial)
python -m pip install --upgrade pip>=23.0
pip install --upgrade setuptools>=65.0 wheel>=0.38.0

# Install PyTorch with CPU support
pip install "torch>=2.2.0,<2.8.0" --index-url https://download.pytorch.org/whl/cpu

# Install core dependencies first
pip install fastapi uvicorn pydantic sqlalchemy alembic
pip install psycopg2-binary asyncpg pgvector redis aioredis
pip install sentence-transformers transformers numpy scikit-learn pandas
pip install underthesea pyvi

# Install remaining dependencies
pip install -r requirements.txt
```

## Why This Happens

1. **Incomplete Virtual Environment**: The virtual environment was created without proper build tools
2. **Corrupted setuptools**: The setuptools package is missing or corrupted
3. **Version Conflicts**: Incompatible versions of pip, setuptools, and wheel
4. **Missing Build Dependencies**: Essential build tools not installed before other packages

## Prevention

Always install build tools first when creating a new virtual environment:

```bash
# After creating and activating venv
python -m pip install --upgrade pip>=23.0
pip install --upgrade setuptools>=65.0 wheel>=0.38.0
# Then install other packages
```

## Verification

After fixing, verify the installation:

```bash
cd backend
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/macOS

python -c "import fastapi, sqlalchemy, torch, sentence_transformers, underthesea; print('All dependencies working!')"
```

## If the Fix Doesn't Work

1. **Check Python Version**: Ensure you have Python 3.9 or higher
   ```bash
   python --version
   ```

2. **Check Virtual Environment**: Make sure you're in the activated virtual environment
   ```bash
   which python  # Linux/macOS
   where python  # Windows
   ```

3. **Clear All Caches**: Clear pip and Python caches
   ```bash
   pip cache purge
   python -m pip cache purge
   ```

4. **Reinstall Python**: If all else fails, reinstall Python completely

## Alternative: Use System Python (Not Recommended)

If virtual environment continues to fail, you can try installing directly to system Python (not recommended for production):

```bash
# Windows
pip install --user --upgrade pip setuptools wheel
pip install --user "torch>=2.2.0,<2.8.0" --index-url https://download.pytorch.org/whl/cpu
pip install --user -r requirements.txt

# Linux/macOS
pip3 install --user --upgrade pip setuptools wheel
pip3 install --user "torch>=2.2.0,<2.8.0" --index-url https://download.pytorch.org/whl/cpu
pip3 install --user -r requirements.txt
```

## Contact Support

If you continue to experience issues:

1. Run the verification script: `python scripts/verify-dependencies.py`
2. Check the full error log
3. Ensure you have the latest Python version
4. Try the automated fix script first

## Updated Installation Scripts

The installation scripts have been updated to prevent this issue by:

1. Installing build tools first
2. Using specific version ranges
3. Better error handling
4. Step-by-step dependency installation

Use the updated scripts:
- `scripts/install-dependencies.bat` (Windows)
- `scripts/install-dependencies.sh` (Linux/macOS)
