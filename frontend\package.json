{"name": "vietlaw-ai-frontend", "version": "1.0.0", "description": "VietLaw.AI - Vietnamese Legal AI Assistant <PERSON><PERSON>", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "format": "prettier --write .", "format:check": "prettier --check .", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "date-fns": "^2.30.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "react-syntax-highlighter": "^15.5.0", "react-pdf": "^7.5.1", "react-dropzone": "^14.2.3", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "react-intersection-observer": "^9.5.3", "react-helmet-async": "^1.3.0", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "@microsoft/office-js": "^1.1.85", "@microsoft/office-js-helpers": "^1.0.1", "recharts": "^2.8.0", "react-chartjs-2": "^5.2.0", "chart.js": "^4.4.0", "react-select": "^5.8.0", "react-datepicker": "^4.24.0", "react-table": "^7.8.0", "@types/react-table": "^7.7.18", "react-beautiful-dnd": "^13.1.1", "@types/react-beautiful-dnd": "^13.1.8"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "typescript": "^5.2.2", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "vitest": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1", "vite-bundle-analyzer": "^0.7.0", "vite-plugin-pwa": "^0.17.4", "workbox-window": "^7.0.0", "@types/react-syntax-highlighter": "^15.5.10", "@types/react-virtualized": "^9.21.29", "@types/react-window": "^1.8.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["vietnamese", "legal", "ai", "assistant", "rag", "law", "react", "typescript", "vite"], "author": "VietLaw.AI Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/vietlaw-ai.git"}, "bugs": {"url": "https://github.com/HectorTa1989/vietlaw-ai/issues"}, "homepage": "https://vietlaw.ai"}